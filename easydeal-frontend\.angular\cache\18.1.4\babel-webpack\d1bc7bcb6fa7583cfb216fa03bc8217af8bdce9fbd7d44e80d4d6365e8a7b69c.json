{"ast": null, "code": "import { Modal } from 'bootstrap';\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/unit.service\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../../shared/view-apartment-model/view-apartment-model.component\";\nimport * as i6 from \"../../../../../pagination/pagination.component\";\nfunction PropertiestableComponent_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 12);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_4_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"type\"));\n    });\n    i0.ɵɵtext(1, \" Unit Type \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"type\"));\n  }\n}\nfunction PropertiestableComponent_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_5_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"city_id\"));\n    });\n    i0.ɵɵtext(1, \" City \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"city_id\"));\n  }\n}\nfunction PropertiestableComponent_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_6_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"area_id\"));\n    });\n    i0.ɵɵtext(1, \" Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"area_id\"));\n  }\n}\nfunction PropertiestableComponent_th_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \" Location on map \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_8_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"compound_name\"));\n    });\n    i0.ɵɵtext(1, \" Compound Name \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"compound_name\"));\n  }\n}\nfunction PropertiestableComponent_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_9_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"mall_name\"));\n    });\n    i0.ɵɵtext(1, \" Mall Name \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"mall_name\"));\n  }\n}\nfunction PropertiestableComponent_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_10_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"building_number\"));\n    });\n    i0.ɵɵtext(1, \" Building Number \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"building_number\"));\n  }\n}\nfunction PropertiestableComponent_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_11_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"unit_number\"));\n    });\n    i0.ɵɵtext(1, \" Unit Number \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"unit_number\"));\n  }\n}\nfunction PropertiestableComponent_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_12_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"floor\"));\n    });\n    i0.ɵɵtext(1, \" Floor \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"floor\"));\n  }\n}\nfunction PropertiestableComponent_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_13_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"unit_area\"));\n    });\n    i0.ɵɵtext(1, \" Unit Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"unit_area\"));\n  }\n}\nfunction PropertiestableComponent_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_14_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"ground_area\"));\n    });\n    i0.ɵɵtext(1, \" Ground Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"ground_area\"));\n  }\n}\nfunction PropertiestableComponent_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_15_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"building_area\"));\n    });\n    i0.ɵɵtext(1, \" Building Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"building_area\"));\n  }\n}\nfunction PropertiestableComponent_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_16_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"number_of_rooms\"));\n    });\n    i0.ɵɵtext(1, \" Rooms \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"number_of_rooms\"));\n  }\n}\nfunction PropertiestableComponent_th_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_17_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"number_of_bathrooms\"));\n    });\n    i0.ɵɵtext(1, \" Bathrooms \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"number_of_bathrooms\"));\n  }\n}\nfunction PropertiestableComponent_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_18_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"number_of_floors\"));\n    });\n    i0.ɵɵtext(1, \" Floors \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"number_of_floors\"));\n  }\n}\nfunction PropertiestableComponent_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_19_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"view\"));\n    });\n    i0.ɵɵtext(1, \" View \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"view\"));\n  }\n}\nfunction PropertiestableComponent_th_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_20_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"finishing_type\"));\n    });\n    i0.ɵɵtext(1, \" Finishing Type \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"finishing_type\"));\n  }\n}\nfunction PropertiestableComponent_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_21_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"delivery_status\"));\n    });\n    i0.ɵɵtext(1, \" Delivery Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"delivery_status\"));\n  }\n}\nfunction PropertiestableComponent_th_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_22_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"legal_status\"));\n    });\n    i0.ɵɵtext(1, \" Legal Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"legal_status\"));\n  }\n}\nfunction PropertiestableComponent_th_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_23_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"financial_status\"));\n    });\n    i0.ɵɵtext(1, \" Financial Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"financial_status\"));\n  }\n}\nfunction PropertiestableComponent_th_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_24_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"activity\"));\n    });\n    i0.ɵɵtext(1, \" Activity \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"activity\"));\n  }\n}\nfunction PropertiestableComponent_th_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_25_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"rent_recurrence\"));\n    });\n    i0.ɵɵtext(1, \" Rent Recurrence \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"rent_recurrence\"));\n  }\n}\nfunction PropertiestableComponent_th_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_26_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"daily_rent\"));\n    });\n    i0.ɵɵtext(1, \" Daily Rent \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"daily_rent\"));\n  }\n}\nfunction PropertiestableComponent_th_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_27_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"monthly_rent\"));\n    });\n    i0.ɵɵtext(1, \" Monthly Rent \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"monthly_rent\"));\n  }\n}\nfunction PropertiestableComponent_th_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 16);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_28_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"other_accessories\"));\n    });\n    i0.ɵɵtext(1, \" Other Accessories \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"other_accessories\"));\n  }\n}\nfunction PropertiestableComponent_th_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \" Unit Plan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_th_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 17);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_tr_32_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.type, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.city == null ? null : property_r27.city.name_en, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.area == null ? null : property_r27.area.name_en, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_tr_32_td_4_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const property_r27 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showImageModal(property_r27.location));\n    });\n    i0.ɵɵelement(2, \"i\", 29);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PropertiestableComponent_tr_32_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.compoundName, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.mallName, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.buildingNumber, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.unitNumber, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.floor, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.unitArea, \" m\\u00B2 \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.groundArea, \" m\\u00B2 \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.buildingArea, \" m\\u00B2 \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.numberOfRooms, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.numberOfBathrooms, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.numberOfFloors, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.view, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.finishingType, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r27.deliveryStatus);\n  }\n}\nfunction PropertiestableComponent_tr_32_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r27.legalStatus);\n  }\n}\nfunction PropertiestableComponent_tr_32_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r27.financialStatus);\n  }\n}\nfunction PropertiestableComponent_tr_32_td_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.activity, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r27.rentRecurrence);\n  }\n}\nfunction PropertiestableComponent_tr_32_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(3, 1, property_r27.dailyRent, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(3, 1, property_r27.monthlyRent, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r27.otherAccessories, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_32_td_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_tr_32_td_26_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const property_r27 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showUnitPlanModal(property_r27.diagram));\n    });\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \" View Plan \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PropertiestableComponent_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, PropertiestableComponent_tr_32_td_1_Template, 3, 1, \"td\", 18)(2, PropertiestableComponent_tr_32_td_2_Template, 3, 1, \"td\", 18)(3, PropertiestableComponent_tr_32_td_3_Template, 3, 1, \"td\", 18)(4, PropertiestableComponent_tr_32_td_4_Template, 3, 0, \"td\", 18)(5, PropertiestableComponent_tr_32_td_5_Template, 3, 1, \"td\", 18)(6, PropertiestableComponent_tr_32_td_6_Template, 3, 1, \"td\", 18)(7, PropertiestableComponent_tr_32_td_7_Template, 3, 1, \"td\", 18)(8, PropertiestableComponent_tr_32_td_8_Template, 3, 1, \"td\", 18)(9, PropertiestableComponent_tr_32_td_9_Template, 3, 1, \"td\", 18)(10, PropertiestableComponent_tr_32_td_10_Template, 3, 1, \"td\", 18)(11, PropertiestableComponent_tr_32_td_11_Template, 3, 1, \"td\", 18)(12, PropertiestableComponent_tr_32_td_12_Template, 3, 1, \"td\", 18)(13, PropertiestableComponent_tr_32_td_13_Template, 3, 1, \"td\", 18)(14, PropertiestableComponent_tr_32_td_14_Template, 3, 1, \"td\", 18)(15, PropertiestableComponent_tr_32_td_15_Template, 3, 1, \"td\", 18)(16, PropertiestableComponent_tr_32_td_16_Template, 3, 1, \"td\", 18)(17, PropertiestableComponent_tr_32_td_17_Template, 3, 1, \"td\", 18)(18, PropertiestableComponent_tr_32_td_18_Template, 3, 1, \"td\", 18)(19, PropertiestableComponent_tr_32_td_19_Template, 3, 1, \"td\", 18)(20, PropertiestableComponent_tr_32_td_20_Template, 3, 1, \"td\", 18)(21, PropertiestableComponent_tr_32_td_21_Template, 3, 1, \"td\", 18)(22, PropertiestableComponent_tr_32_td_22_Template, 3, 1, \"td\", 18)(23, PropertiestableComponent_tr_32_td_23_Template, 4, 6, \"td\", 18)(24, PropertiestableComponent_tr_32_td_24_Template, 4, 6, \"td\", 18)(25, PropertiestableComponent_tr_32_td_25_Template, 3, 1, \"td\", 18)(26, PropertiestableComponent_tr_32_td_26_Template, 4, 0, \"td\", 18);\n    i0.ɵɵelementStart(27, \"td\", 19)(28, \"div\", 20)(29, \"button\", 21);\n    i0.ɵɵelement(30, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"ul\", 23)(32, \"li\")(33, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_tr_32_Template_button_click_33_listener() {\n      const property_r27 = i0.ɵɵrestoreView(_r26).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewProperty(property_r27));\n    });\n    i0.ɵɵelement(34, \"i\", 25);\n    i0.ɵɵtext(35, \" View unit Details \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"type\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"city\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"area\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"location\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"compoundName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"mallName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"buildingNumber\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitNumber\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"floor\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"groundArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"buildingArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"numberOfRooms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"numberOfBathrooms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"numberOfFloors\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"view\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"finishingType\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"deliveryStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"legalStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"financialStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"activity\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"rentRecurrence\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"dailyRent\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"monthlyRent\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"otherAccessories\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitPlan\"));\n  }\n}\nexport class PropertiestableComponent extends BaseGridComponent {\n  cd;\n  unitService;\n  sanitizer;\n  router;\n  //session\n  brokerId;\n  appliedFilters;\n  selectedImage = null;\n  selectedLocation = null;\n  // Dynamic column visibility\n  visibleColumns = [];\n  constructor(cd, unitService, sanitizer, router) {\n    super(cd);\n    this.cd = cd;\n    this.unitService = unitService;\n    this.sanitizer = sanitizer;\n    this.router = router;\n    const userJson = localStorage.getItem('currentUser');\n    let user = userJson ? JSON.parse(userJson) : null;\n    this.brokerId = user?.brokerId;\n    this.setService(unitService);\n    this.orderBy = 'id';\n    this.orderDir = 'desc';\n    this.page.filters = {\n      brokerId: this.brokerId\n    };\n  }\n  ngOnChanges(changes) {\n    if (changes.appliedFilters && !changes.appliedFilters.firstChange) {\n      this.page.filters = {\n        brokerId: this.brokerId,\n        ...this.appliedFilters\n      };\n      this.updateVisibleColumns();\n      this.reloadTable(this.page);\n    }\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.updateVisibleColumns();\n  }\n  updateVisibleColumns() {\n    if (this.appliedFilters?.compoundType && this.appliedFilters?.propertyType) {\n      this.visibleColumns = this.getFieldsToShow();\n    } else {\n      // Show all columns by default\n      this.visibleColumns = ['type', 'city', 'area', 'location', 'buildingNumber', 'view', 'floor', 'deliveryDate', 'finishingType', 'status', 'unitPlan', 'otherAccessories'];\n    }\n  }\n  // Get fields to show based on compound type and property type\n  getFieldsToShow() {\n    const compoundType = this.appliedFilters?.compoundType;\n    const type = this.appliedFilters?.propertyType;\n    // Base columns that are always shown\n    const baseColumns = ['type', 'city', 'area', 'location'];\n    const actionColumns = ['unitPlan', 'actions'];\n    let specificColumns = [];\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\n      specificColumns = ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories'];\n    } else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\n      specificColumns = ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories'];\n    } else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      specificColumns = ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories'];\n    } else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\n      specificColumns = ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories'];\n    } else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\n      specificColumns = ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories'];\n    } else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\n      specificColumns = ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver'];\n    } else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\n      specificColumns = ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver'];\n    } else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      specificColumns = ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver'];\n    } else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {\n      specificColumns = ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'];\n    } else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {\n      specificColumns = ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];\n    }\n    return [...baseColumns, ...specificColumns, ...actionColumns];\n  }\n  // Check if a specific column should be shown\n  shouldShowColumn(columnName) {\n    return this.visibleColumns.includes(columnName);\n  }\n  showImageModal(location) {\n    if (!location || location.trim() === '') {\n      Swal.fire({\n        title: 'Warning',\n        text: 'No location available',\n        icon: 'warning',\n        confirmButtonText: 'OK'\n      });\n      return;\n    }\n    if (location.includes('maps.google.com') || location.includes('maps.app.goo.gl')) {\n      window.open(location, '_blank');\n      return;\n    }\n    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(location)}`;\n    window.open(mapUrl, '_blank');\n  }\n  //****************************** */\n  selectedUnitPlanImage = null;\n  showUnitPlanModal(imgPath) {\n    this.selectedUnitPlanImage = imgPath;\n    const modalElement = document.getElementById('viewUnitPlanModal');\n    if (modalElement) {\n      const modal = new Modal(modalElement);\n      modal.show();\n    }\n  }\n  viewProperty(unitService) {\n    this.router.navigate(['/developer/projects/models/units/details'], {\n      queryParams: {\n        unitId: unitService.id\n      }\n    });\n  }\n  static ɵfac = function PropertiestableComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PropertiestableComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UnitService), i0.ɵɵdirectiveInject(i2.DomSanitizer), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PropertiestableComponent,\n    selectors: [[\"app-propertiestable\"]],\n    inputs: {\n      appliedFilters: \"appliedFilters\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 36,\n    vars: 32,\n    consts: [[1, \"table-responsive\", \"mb-5\"], [1, \"table\", \"table-row-bordered\", \"table-row-gray-100\", \"align-middle\", \"gs-0\", \"gy-3\", \"mt-5\"], [1, \"fw-bold\", \"bg-light-dark-blue\", \"text-dark-blue\", \"me-1\", \"ms-1\"], [\"class\", \"min-w-150px cursor-pointer ps-4 rounded-start\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"min-w-150px cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"min-w-150px\", 4, \"ngIf\"], [\"class\", \"min-w-200px cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"min-w-50px text-end rounded-end pe-4\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"m-2\"], [3, \"pageChange\", \"totalItems\", \"itemsPerPage\", \"currentPage\"], [3, \"selectedUnitPlanImage\"], [1, \"min-w-150px\", \"cursor-pointer\", \"ps-4\", \"rounded-start\", 3, \"click\"], [1, \"ms-1\", \"text-primary\", \"fw-bold\"], [1, \"min-w-150px\", \"cursor-pointer\", 3, \"click\"], [1, \"min-w-150px\"], [1, \"min-w-200px\", \"cursor-pointer\", 3, \"click\"], [1, \"min-w-50px\", \"text-end\", \"rounded-end\", \"pe-4\"], [4, \"ngIf\"], [1, \"text-end\", \"pe-4\"], [1, \"dropdown\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-color-primary\", \"btn-active-light-primary\"], [1, \"fa-solid\", \"fa-ellipsis-vertical\"], [1, \"dropdown-menu\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fa-solid\", \"fa-eye\", \"me-2\"], [1, \"text-gray-900\", \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\", \"ps-4\"], [1, \"text-gray-900\", \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\"], [\"data-bs-toggle\", \"tooltip\", \"title\", \"View on map\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-light-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-map-location-dot\"], [1, \"badge\", \"badge-light-success\"], [1, \"badge\", \"badge-light-info\"], [1, \"badge\", \"badge-light-warning\"], [1, \"badge\", \"badge-light-primary\"], [1, \"text-success\", \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\"], [1, \"btn\", \"btn-sm\", \"btn-light-info\", 3, \"click\"], [1, \"fa-solid\", \"fa-file-image\", \"me-1\"]],\n    template: function PropertiestableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"table\", 1)(2, \"thead\")(3, \"tr\", 2);\n        i0.ɵɵtemplate(4, PropertiestableComponent_th_4_Template, 4, 1, \"th\", 3)(5, PropertiestableComponent_th_5_Template, 4, 1, \"th\", 4)(6, PropertiestableComponent_th_6_Template, 4, 1, \"th\", 4)(7, PropertiestableComponent_th_7_Template, 2, 0, \"th\", 5)(8, PropertiestableComponent_th_8_Template, 4, 1, \"th\", 4)(9, PropertiestableComponent_th_9_Template, 4, 1, \"th\", 4)(10, PropertiestableComponent_th_10_Template, 4, 1, \"th\", 4)(11, PropertiestableComponent_th_11_Template, 4, 1, \"th\", 4)(12, PropertiestableComponent_th_12_Template, 4, 1, \"th\", 4)(13, PropertiestableComponent_th_13_Template, 4, 1, \"th\", 4)(14, PropertiestableComponent_th_14_Template, 4, 1, \"th\", 4)(15, PropertiestableComponent_th_15_Template, 4, 1, \"th\", 4)(16, PropertiestableComponent_th_16_Template, 4, 1, \"th\", 4)(17, PropertiestableComponent_th_17_Template, 4, 1, \"th\", 4)(18, PropertiestableComponent_th_18_Template, 4, 1, \"th\", 4)(19, PropertiestableComponent_th_19_Template, 4, 1, \"th\", 4)(20, PropertiestableComponent_th_20_Template, 4, 1, \"th\", 4)(21, PropertiestableComponent_th_21_Template, 4, 1, \"th\", 4)(22, PropertiestableComponent_th_22_Template, 4, 1, \"th\", 4)(23, PropertiestableComponent_th_23_Template, 4, 1, \"th\", 4)(24, PropertiestableComponent_th_24_Template, 4, 1, \"th\", 4)(25, PropertiestableComponent_th_25_Template, 4, 1, \"th\", 4)(26, PropertiestableComponent_th_26_Template, 4, 1, \"th\", 4)(27, PropertiestableComponent_th_27_Template, 4, 1, \"th\", 4)(28, PropertiestableComponent_th_28_Template, 4, 1, \"th\", 6)(29, PropertiestableComponent_th_29_Template, 2, 0, \"th\", 5)(30, PropertiestableComponent_th_30_Template, 2, 0, \"th\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"tbody\");\n        i0.ɵɵtemplate(32, PropertiestableComponent_tr_32_Template, 36, 26, \"tr\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 9)(34, \"app-pagination\", 10);\n        i0.ɵɵlistener(\"pageChange\", function PropertiestableComponent_Template_app_pagination_pageChange_34_listener($event) {\n          return ctx.onPageChange($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(35, \"app-view-apartment-model\", 11);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"type\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"city\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"area\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"location\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"compoundName\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"mallName\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"buildingNumber\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitNumber\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"floor\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitArea\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"groundArea\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"buildingArea\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"numberOfRooms\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"numberOfBathrooms\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"numberOfFloors\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"view\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"finishingType\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"deliveryStatus\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"legalStatus\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"financialStatus\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"activity\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"rentRecurrence\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"dailyRent\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"monthlyRent\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"otherAccessories\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitPlan\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"actions\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.rows);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"totalItems\", ctx.page.totalElements)(\"itemsPerPage\", ctx.page.size)(\"currentPage\", ctx.page.pageNumber);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"selectedUnitPlanImage\", ctx.selectedUnitPlanImage);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i5.ViewApartmentModelComponent, i6.PaginationComponent, i4.CurrencyPipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Modal", "BaseGridComponent", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "PropertiestableComponent_th_4_Template_th_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "sortData", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "getSortArrow", "PropertiestableComponent_th_5_Template_th_click_0_listener", "_r3", "PropertiestableComponent_th_6_Template_th_click_0_listener", "_r4", "PropertiestableComponent_th_8_Template_th_click_0_listener", "_r5", "PropertiestableComponent_th_9_Template_th_click_0_listener", "_r6", "PropertiestableComponent_th_10_Template_th_click_0_listener", "_r7", "PropertiestableComponent_th_11_Template_th_click_0_listener", "_r8", "PropertiestableComponent_th_12_Template_th_click_0_listener", "_r9", "PropertiestableComponent_th_13_Template_th_click_0_listener", "_r10", "PropertiestableComponent_th_14_Template_th_click_0_listener", "_r11", "PropertiestableComponent_th_15_Template_th_click_0_listener", "_r12", "PropertiestableComponent_th_16_Template_th_click_0_listener", "_r13", "PropertiestableComponent_th_17_Template_th_click_0_listener", "_r14", "PropertiestableComponent_th_18_Template_th_click_0_listener", "_r15", "PropertiestableComponent_th_19_Template_th_click_0_listener", "_r16", "PropertiestableComponent_th_20_Template_th_click_0_listener", "_r17", "PropertiestableComponent_th_21_Template_th_click_0_listener", "_r18", "PropertiestableComponent_th_22_Template_th_click_0_listener", "_r19", "PropertiestableComponent_th_23_Template_th_click_0_listener", "_r20", "PropertiestableComponent_th_24_Template_th_click_0_listener", "_r21", "PropertiestableComponent_th_25_Template_th_click_0_listener", "_r22", "PropertiestableComponent_th_26_Template_th_click_0_listener", "_r23", "PropertiestableComponent_th_27_Template_th_click_0_listener", "_r24", "PropertiestableComponent_th_28_Template_th_click_0_listener", "_r25", "ɵɵtextInterpolate1", "property_r27", "type", "city", "name_en", "area", "PropertiestableComponent_tr_32_td_4_Template_button_click_1_listener", "_r28", "$implicit", "showImageModal", "location", "ɵɵelement", "compoundName", "mallName", "buildingNumber", "unitNumber", "floor", "unitArea", "groundArea", "buildingArea", "numberOfRooms", "numberOfBathrooms", "numberOfFloors", "view", "finishingType", "deliveryStatus", "legalStatus", "financialStatus", "activity", "rentRecurrence", "ɵɵpipeBind4", "dailyRent", "monthlyRent", "otherAccessories", "PropertiestableComponent_tr_32_td_26_Template_button_click_1_listener", "_r29", "showUnitPlanModal", "diagram", "ɵɵtemplate", "PropertiestableComponent_tr_32_td_1_Template", "PropertiestableComponent_tr_32_td_2_Template", "PropertiestableComponent_tr_32_td_3_Template", "PropertiestableComponent_tr_32_td_4_Template", "PropertiestableComponent_tr_32_td_5_Template", "PropertiestableComponent_tr_32_td_6_Template", "PropertiestableComponent_tr_32_td_7_Template", "PropertiestableComponent_tr_32_td_8_Template", "PropertiestableComponent_tr_32_td_9_Template", "PropertiestableComponent_tr_32_td_10_Template", "PropertiestableComponent_tr_32_td_11_Template", "PropertiestableComponent_tr_32_td_12_Template", "PropertiestableComponent_tr_32_td_13_Template", "PropertiestableComponent_tr_32_td_14_Template", "PropertiestableComponent_tr_32_td_15_Template", "PropertiestableComponent_tr_32_td_16_Template", "PropertiestableComponent_tr_32_td_17_Template", "PropertiestableComponent_tr_32_td_18_Template", "PropertiestableComponent_tr_32_td_19_Template", "PropertiestableComponent_tr_32_td_20_Template", "PropertiestableComponent_tr_32_td_21_Template", "PropertiestableComponent_tr_32_td_22_Template", "PropertiestableComponent_tr_32_td_23_Template", "PropertiestableComponent_tr_32_td_24_Template", "PropertiestableComponent_tr_32_td_25_Template", "PropertiestableComponent_tr_32_td_26_Template", "PropertiestableComponent_tr_32_Template_button_click_33_listener", "_r26", "viewProperty", "ɵɵproperty", "shouldShowColumn", "PropertiestableComponent", "cd", "unitService", "sanitizer", "router", "brokerId", "appliedFilters", "selectedImage", "selectedLocation", "visibleColumns", "constructor", "userJson", "localStorage", "getItem", "user", "JSON", "parse", "setService", "orderBy", "orderDir", "page", "filters", "ngOnChanges", "changes", "firstChange", "updateVisibleColumns", "reloadTable", "ngOnInit", "compoundType", "propertyType", "getFieldsToShow", "baseColumns", "actionColumns", "specificColumns", "columnName", "includes", "trim", "fire", "title", "text", "icon", "confirmButtonText", "window", "open", "mapUrl", "encodeURIComponent", "selectedUnitPlanImage", "imgPath", "modalElement", "document", "getElementById", "modal", "show", "navigate", "queryParams", "unitId", "id", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "UnitService", "i2", "Dom<PERSON><PERSON><PERSON>zer", "i3", "Router", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "PropertiestableComponent_Template", "rf", "ctx", "PropertiestableComponent_th_4_Template", "PropertiestableComponent_th_5_Template", "PropertiestableComponent_th_6_Template", "PropertiestableComponent_th_7_Template", "PropertiestableComponent_th_8_Template", "PropertiestableComponent_th_9_Template", "PropertiestableComponent_th_10_Template", "PropertiestableComponent_th_11_Template", "PropertiestableComponent_th_12_Template", "PropertiestableComponent_th_13_Template", "PropertiestableComponent_th_14_Template", "PropertiestableComponent_th_15_Template", "PropertiestableComponent_th_16_Template", "PropertiestableComponent_th_17_Template", "PropertiestableComponent_th_18_Template", "PropertiestableComponent_th_19_Template", "PropertiestableComponent_th_20_Template", "PropertiestableComponent_th_21_Template", "PropertiestableComponent_th_22_Template", "PropertiestableComponent_th_23_Template", "PropertiestableComponent_th_24_Template", "PropertiestableComponent_th_25_Template", "PropertiestableComponent_th_26_Template", "PropertiestableComponent_th_27_Template", "PropertiestableComponent_th_28_Template", "PropertiestableComponent_th_29_Template", "PropertiestableComponent_th_30_Template", "PropertiestableComponent_tr_32_Template", "PropertiestableComponent_Template_app_pagination_pageChange_34_listener", "$event", "onPageChange", "rows", "totalElements", "size", "pageNumber"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\propertiestable\\propertiestable.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\propertiestable\\propertiestable.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input, SimpleChanges } from '@angular/core';\r\nimport { Modal } from 'bootstrap';\r\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\r\nimport { UnitService } from '../../../services/unit.service';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\nimport Swal from 'sweetalert2';\r\nimport { Router } from '@angular/router';\r\n\r\n\r\n@Component({\r\n  selector: 'app-propertiestable',\r\n  templateUrl: './propertiestable.component.html',\r\n  styleUrl: './propertiestable.component.scss',\r\n})\r\nexport class PropertiestableComponent extends BaseGridComponent {\r\n\r\n  //session\r\n  brokerId: number;\r\n\r\n  @Input() appliedFilters: any;\r\n  selectedImage: string | null = null;\r\n  selectedLocation: SafeResourceUrl | null = null;\r\n\r\n  // Dynamic column visibility\r\n  visibleColumns: string[] = [];\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    protected unitService: UnitService,\r\n    private sanitizer: <PERSON><PERSON>anitizer,\r\n    private router: Router\r\n  ) {\r\n    super(cd);\r\n    const userJson = localStorage.getItem('currentUser');\r\n    let user = userJson ? JSON.parse(userJson) : null;\r\n    this.brokerId = user?.brokerId;\r\n    this.setService(unitService);\r\n    this.orderBy = 'id';\r\n    this.orderDir = 'desc';\r\n    this.page.filters = { brokerId: this.brokerId };\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.appliedFilters && !changes.appliedFilters.firstChange) {\r\n      this.page.filters = { brokerId: this.brokerId, ...this.appliedFilters}\r\n      this.updateVisibleColumns();\r\n      this.reloadTable(this.page);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    super.ngOnInit();\r\n    this.updateVisibleColumns();\r\n  }\r\n\r\n  updateVisibleColumns(): void {\r\n    if (this.appliedFilters?.compoundType && this.appliedFilters?.propertyType) {\r\n      this.visibleColumns = this.getFieldsToShow();\r\n    } else {\r\n      // Show all columns by default\r\n      this.visibleColumns = [\r\n        'type', 'city', 'area', 'location', 'buildingNumber', 'view', 'floor',\r\n        'deliveryDate', 'finishingType', 'status', 'unitPlan', 'otherAccessories'\r\n      ];\r\n    }\r\n  }\r\n\r\n  // Get fields to show based on compound type and property type\r\n  getFieldsToShow(): string[] {\r\n    const compoundType = this.appliedFilters?.compoundType;\r\n    const type = this.appliedFilters?.propertyType;\r\n\r\n    // Base columns that are always shown\r\n    const baseColumns = ['type', 'city', 'area', 'location'];\r\n    const actionColumns = ['unitPlan', 'actions'];\r\n\r\n    let specificColumns: string[] = [];\r\n\r\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\r\n      specificColumns = ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\r\n      specificColumns = ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\r\n      specificColumns = ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\r\n      specificColumns = ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\r\n      specificColumns = ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\r\n      specificColumns = ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\r\n      specificColumns = ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\r\n      specificColumns = ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver'];\r\n    }\r\n    else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {\r\n      specificColumns = ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'];\r\n    }\r\n    else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {\r\n      specificColumns = ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];\r\n    }\r\n\r\n    return [...baseColumns, ...specificColumns, ...actionColumns];\r\n  }\r\n\r\n  // Check if a specific column should be shown\r\n  shouldShowColumn(columnName: string): boolean {\r\n    return this.visibleColumns.includes(columnName);\r\n  }\r\n\r\n  showImageModal(location: string) {\r\n    if (!location || location.trim() === '') {\r\n      Swal.fire({\r\n        title: 'Warning',\r\n        text: 'No location available',\r\n        icon: 'warning',\r\n        confirmButtonText: 'OK',\r\n      });\r\n      return;\r\n    }\r\n    if (\r\n      location.includes('maps.google.com') ||\r\n      location.includes('maps.app.goo.gl')\r\n    ) {\r\n      window.open(location, '_blank');\r\n      return;\r\n    }\r\n\r\n    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(\r\n      location\r\n    )}`;\r\n    window.open(mapUrl, '_blank');\r\n  }\r\n\r\n  //****************************** */\r\n\r\n   selectedUnitPlanImage: string | null = null;\r\n\r\n  showUnitPlanModal(imgPath: string) {\r\n    this.selectedUnitPlanImage = imgPath;\r\n\r\n    const modalElement = document.getElementById('viewUnitPlanModal');\r\n    if (modalElement) {\r\n      const modal = new Modal(modalElement);\r\n      modal.show();\r\n    }\r\n  }\r\n\r\n  viewProperty(unitService: any) {\r\n    this.router.navigate(['/developer/projects/models/units/details'], {\r\n      queryParams: { unitId: unitService.id }\r\n    });\r\n\r\n  }\r\n//************************************** */\r\n\r\n  //  sortData(column: string) {\r\n  //    if (this.orderBy === column) {\r\n  //     this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';\r\n  //   } else {\r\n  //     this.orderBy = column;\r\n  //     this.orderDir = 'asc';\r\n  //   }\r\n\r\n  //    this.page.orderBy = this.orderBy;\r\n  //   this.page.orderDir = this.orderDir;\r\n  //   this.page.pageNumber = 0;\r\n  //   this.reloadTable(this.page);\r\n  // }\r\n\r\n  //  getSortArrow(column: string): string {\r\n  //   if (this.orderBy !== column) {\r\n  //     return '';\r\n  //   }\r\n  //   return this.orderDir === 'asc' ? '↑' : '↓';\r\n  // }\r\n}\r\n", "<div class=\"table-responsive mb-5\">\r\n  <table class=\"table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5\">\r\n    <thead>\r\n      <tr class=\"fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1\">\r\n        <!-- Base columns - always shown -->\r\n        <th class=\"min-w-150px cursor-pointer ps-4 rounded-start\" (click)=\"sortData('type')\"\r\n          *ngIf=\"shouldShowColumn('type')\">\r\n          Unit Type\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('type') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('city_id')\" *ngIf=\"shouldShowColumn('city')\">\r\n          City\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('city_id') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('area_id')\" *ngIf=\"shouldShowColumn('area')\">\r\n          Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('area_id') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px\" *ngIf=\"shouldShowColumn('location')\">\r\n          Location on map\r\n        </th>\r\n\r\n        <!-- Dynamic columns based on filter -->\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('compound_name')\"\r\n          *ngIf=\"shouldShowColumn('compoundName')\">\r\n          Compound Name\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('compound_name') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('mall_name')\" *ngIf=\"shouldShowColumn('mallName')\">\r\n          Mall Name\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('mall_name') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('building_number')\"\r\n          *ngIf=\"shouldShowColumn('buildingNumber')\">\r\n          Building Number\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('building_number') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('unit_number')\" *ngIf=\"shouldShowColumn('unitNumber')\">\r\n          Unit Number\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('unit_number') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('floor')\" *ngIf=\"shouldShowColumn('floor')\">\r\n          Floor\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('floor') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('unit_area')\" *ngIf=\"shouldShowColumn('unitArea')\">\r\n          Unit Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('unit_area') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('ground_area')\" *ngIf=\"shouldShowColumn('groundArea')\">\r\n          Ground Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('ground_area') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('building_area')\"\r\n          *ngIf=\"shouldShowColumn('buildingArea')\">\r\n          Building Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('building_area') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('number_of_rooms')\"\r\n          *ngIf=\"shouldShowColumn('numberOfRooms')\">\r\n          Rooms\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('number_of_rooms') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('number_of_bathrooms')\"\r\n          *ngIf=\"shouldShowColumn('numberOfBathrooms')\">\r\n          Bathrooms\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('number_of_bathrooms') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('number_of_floors')\"\r\n          *ngIf=\"shouldShowColumn('numberOfFloors')\">\r\n          Floors\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('number_of_floors') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('view')\" *ngIf=\"shouldShowColumn('view')\">\r\n          View\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('view') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('finishing_type')\"\r\n          *ngIf=\"shouldShowColumn('finishingType')\">\r\n          Finishing Type\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('finishing_type') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('delivery_status')\"\r\n          *ngIf=\"shouldShowColumn('deliveryStatus')\">\r\n          Delivery Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('delivery_status') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('legal_status')\"\r\n          *ngIf=\"shouldShowColumn('legalStatus')\">\r\n          Legal Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('legal_status') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('financial_status')\"\r\n          *ngIf=\"shouldShowColumn('financialStatus')\">\r\n          Financial Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('financial_status') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('activity')\" *ngIf=\"shouldShowColumn('activity')\">\r\n          Activity\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('activity') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('rent_recurrence')\"\r\n          *ngIf=\"shouldShowColumn('rentRecurrence')\">\r\n          Rent Recurrence\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('rent_recurrence') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('daily_rent')\" *ngIf=\"shouldShowColumn('dailyRent')\">\r\n          Daily Rent\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('daily_rent') }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('monthly_rent')\"\r\n          *ngIf=\"shouldShowColumn('monthlyRent')\">\r\n          Monthly Rent\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('monthly_rent') }}</span>\r\n        </th>\r\n        <th class=\"min-w-200px cursor-pointer\" (click)=\"sortData('other_accessories')\"\r\n          *ngIf=\"shouldShowColumn('otherAccessories')\">\r\n          Other Accessories\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('other_accessories') }}</span>\r\n        </th>\r\n\r\n        <!-- Always show unit plan and actions -->\r\n        <th class=\"min-w-150px\" *ngIf=\"shouldShowColumn('unitPlan')\">\r\n          Unit Plan\r\n        </th>\r\n        <th class=\"min-w-50px text-end rounded-end pe-4\" *ngIf=\"shouldShowColumn('actions')\">Actions</th>\r\n      </tr>\r\n    </thead>\r\n    <tbody>\r\n      <tr *ngFor=\"let property of rows\">\r\n        <!-- Base columns - always shown -->\r\n        <td *ngIf=\"shouldShowColumn('type')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6 ps-4\">\r\n            {{ property.type }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('city')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.city?.name_en }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('area')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.area?.name_en }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('location')\">\r\n          <button class=\"btn btn-icon btn-sm btn-light-primary\" data-bs-toggle=\"tooltip\" title=\"View on map\"\r\n            (click)=\"showImageModal(property.location)\">\r\n            <i class=\"fa-solid fa-map-location-dot\"></i>\r\n          </button>\r\n        </td>\r\n\r\n        <!-- Dynamic columns based on filter -->\r\n        <td *ngIf=\"shouldShowColumn('compoundName')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.compoundName }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('mallName')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.mallName }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('buildingNumber')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.buildingNumber }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('unitNumber')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.unitNumber }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('floor')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.floor }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('unitArea')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.unitArea }} m²\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('groundArea')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.groundArea }} m²\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('buildingArea')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.buildingArea }} m²\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('numberOfRooms')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.numberOfRooms }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('numberOfBathrooms')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.numberOfBathrooms }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('numberOfFloors')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.numberOfFloors }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('view')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.view }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('finishingType')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.finishingType }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('deliveryStatus')\">\r\n          <span class=\"badge badge-light-success\">{{ property.deliveryStatus }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('legalStatus')\">\r\n          <span class=\"badge badge-light-info\">{{ property.legalStatus }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('financialStatus')\">\r\n          <span class=\"badge badge-light-warning\">{{ property.financialStatus }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('activity')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.activity }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('rentRecurrence')\">\r\n          <span class=\"badge badge-light-primary\">{{ property.rentRecurrence }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('dailyRent')\">\r\n          <span class=\"text-success fw-bold d-block mb-1 fs-6\">\r\n            {{ property.dailyRent | currency:'EGP':'symbol':'1.0-0' }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('monthlyRent')\">\r\n          <span class=\"text-success fw-bold d-block mb-1 fs-6\">\r\n            {{ property.monthlyRent | currency:'EGP':'symbol':'1.0-0' }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('otherAccessories')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.otherAccessories }}\r\n          </span>\r\n        </td>\r\n\r\n        <!-- Always show unit plan and actions -->\r\n        <td *ngIf=\"shouldShowColumn('unitPlan')\">\r\n          <button class=\"btn btn-sm btn-light-info\" (click)=\"showUnitPlanModal(property.diagram)\">\r\n            <i class=\"fa-solid fa-file-image me-1\"></i> View Plan\r\n          </button>\r\n        </td>\r\n        <td class=\"text-end pe-4\">\r\n          <div class=\"dropdown\">\r\n            <button class=\"btn btn-sm btn-icon btn-color-primary btn-active-light-primary\" type=\"button\"\r\n              data-bs-toggle=\"dropdown\">\r\n              <i class=\"fa-solid fa-ellipsis-vertical\"></i>\r\n            </button>\r\n            <ul class=\"dropdown-menu\">\r\n              <li>\r\n                <button class=\"dropdown-item\" (click)=\"viewProperty(property)\">\r\n                  <i class=\"fa-solid fa-eye me-2\"></i> View unit Details\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table>\r\n  <div class=\"m-2\">\r\n    <app-pagination [totalItems]=\"page.totalElements\" [itemsPerPage]=\"page.size\" [currentPage]=\"page.pageNumber\"\r\n      (pageChange)=\"onPageChange($event)\">\r\n    </app-pagination>\r\n  </div>\r\n</div>\r\n\r\n<app-view-apartment-model [selectedUnitPlanImage]=\"selectedUnitPlanImage\"></app-view-apartment-model>"], "mappings": "AACA,SAASA,KAAK,QAAQ,WAAW;AACjC,SAASC,iBAAiB,QAAQ,oDAAoD;AAGtF,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;ICAtBC,EAAA,CAAAC,cAAA,aACmC;IADuBD,EAAA,CAAAE,UAAA,mBAAAC,2DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,MAAM,CAAC;IAAA,EAAC;IAElFT,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA0B;IACpEV,EADoE,CAAAW,YAAA,EAAO,EACtE;;;;IADqCX,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,SAA0B;;;;;;IAEpEd,EAAA,CAAAC,cAAA,aAAsG;IAA/DD,EAAA,CAAAE,UAAA,mBAAAa,2DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,SAAS,CAAC;IAAA,EAAC;IAClET,EAAA,CAAAU,MAAA,aACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA6B;IACvEV,EADuE,CAAAW,YAAA,EAAO,EACzE;;;;IADqCX,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,YAA6B;;;;;;IAEvEd,EAAA,CAAAC,cAAA,aAAsG;IAA/DD,EAAA,CAAAE,UAAA,mBAAAe,2DAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,SAAS,CAAC;IAAA,EAAC;IAClET,EAAA,CAAAU,MAAA,aACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA6B;IACvEV,EADuE,CAAAW,YAAA,EAAO,EACzE;;;;IADqCX,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,YAA6B;;;;;IAEvEd,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAU,MAAA,wBACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;;IAGLX,EAAA,CAAAC,cAAA,aAC2C;IADJD,EAAA,CAAAE,UAAA,mBAAAiB,2DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,eAAe,CAAC;IAAA,EAAC;IAExET,EAAA,CAAAU,MAAA,sBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAmC;IAC7EV,EAD6E,CAAAW,YAAA,EAAO,EAC/E;;;;IADqCX,EAAA,CAAAY,SAAA,GAAmC;IAAnCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,kBAAmC;;;;;;IAE7Ed,EAAA,CAAAC,cAAA,aAA4G;IAArED,EAAA,CAAAE,UAAA,mBAAAmB,2DAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,WAAW,CAAC;IAAA,EAAC;IACpET,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA+B;IACzEV,EADyE,CAAAW,YAAA,EAAO,EAC3E;;;;IADqCX,EAAA,CAAAY,SAAA,GAA+B;IAA/BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,cAA+B;;;;;;IAEzEd,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAAqB,4DAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAqC;IAC/EV,EAD+E,CAAAW,YAAA,EAAO,EACjF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAAqC;;;;;;IAE/Ed,EAAA,CAAAC,cAAA,aAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAAuB,4DAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,aAAa,CAAC;IAAA,EAAC;IACtET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAiC;IAC3EV,EAD2E,CAAAW,YAAA,EAAO,EAC7E;;;;IADqCX,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,gBAAiC;;;;;;IAE3Ed,EAAA,CAAAC,cAAA,aAAqG;IAA9DD,EAAA,CAAAE,UAAA,mBAAAyB,4DAAA;MAAA3B,EAAA,CAAAI,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,OAAO,CAAC;IAAA,EAAC;IAChET,EAAA,CAAAU,MAAA,cACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA2B;IACrEV,EADqE,CAAAW,YAAA,EAAO,EACvE;;;;IADqCX,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,UAA2B;;;;;;IAErEd,EAAA,CAAAC,cAAA,aAA4G;IAArED,EAAA,CAAAE,UAAA,mBAAA2B,4DAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA0B,IAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,WAAW,CAAC;IAAA,EAAC;IACpET,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA+B;IACzEV,EADyE,CAAAW,YAAA,EAAO,EAC3E;;;;IADqCX,EAAA,CAAAY,SAAA,GAA+B;IAA/BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,cAA+B;;;;;;IAEzEd,EAAA,CAAAC,cAAA,aAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAA6B,4DAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA4B,IAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,aAAa,CAAC;IAAA,EAAC;IACtET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAiC;IAC3EV,EAD2E,CAAAW,YAAA,EAAO,EAC7E;;;;IADqCX,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,gBAAiC;;;;;;IAE3Ed,EAAA,CAAAC,cAAA,aAC2C;IADJD,EAAA,CAAAE,UAAA,mBAAA+B,4DAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,IAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,eAAe,CAAC;IAAA,EAAC;IAExET,EAAA,CAAAU,MAAA,sBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAmC;IAC7EV,EAD6E,CAAAW,YAAA,EAAO,EAC/E;;;;IADqCX,EAAA,CAAAY,SAAA,GAAmC;IAAnCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,kBAAmC;;;;;;IAE7Ed,EAAA,CAAAC,cAAA,aAC4C;IADLD,EAAA,CAAAE,UAAA,mBAAAiC,4DAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,IAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,cACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAqC;IAC/EV,EAD+E,CAAAW,YAAA,EAAO,EACjF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAAqC;;;;;;IAE/Ed,EAAA,CAAAC,cAAA,aACgD;IADTD,EAAA,CAAAE,UAAA,mBAAAmC,4DAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,IAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,qBAAqB,CAAC;IAAA,EAAC;IAE9ET,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAyC;IACnFV,EADmF,CAAAW,YAAA,EAAO,EACrF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAyC;IAAzCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,wBAAyC;;;;;;IAEnFd,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAAqC,4DAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,IAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,kBAAkB,CAAC;IAAA,EAAC;IAE3ET,EAAA,CAAAU,MAAA,eACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAsC;IAChFV,EADgF,CAAAW,YAAA,EAAO,EAClF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,qBAAsC;;;;;;IAEhFd,EAAA,CAAAC,cAAA,aAAmG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAuC,4DAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsC,IAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,MAAM,CAAC;IAAA,EAAC;IAC/DT,EAAA,CAAAU,MAAA,aACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA0B;IACpEV,EADoE,CAAAW,YAAA,EAAO,EACtE;;;;IADqCX,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,SAA0B;;;;;;IAEpEd,EAAA,CAAAC,cAAA,aAC4C;IADLD,EAAA,CAAAE,UAAA,mBAAAyC,4DAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAwC,IAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,gBAAgB,CAAC;IAAA,EAAC;IAEzET,EAAA,CAAAU,MAAA,uBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAoC;IAC9EV,EAD8E,CAAAW,YAAA,EAAO,EAChF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,mBAAoC;;;;;;IAE9Ed,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAA2C,4DAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAqC;IAC/EV,EAD+E,CAAAW,YAAA,EAAO,EACjF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAAqC;;;;;;IAE/Ed,EAAA,CAAAC,cAAA,aAC0C;IADHD,EAAA,CAAAE,UAAA,mBAAA6C,4DAAA;MAAA/C,EAAA,CAAAI,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,cAAc,CAAC;IAAA,EAAC;IAEvET,EAAA,CAAAU,MAAA,qBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAkC;IAC5EV,EAD4E,CAAAW,YAAA,EAAO,EAC9E;;;;IADqCX,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,iBAAkC;;;;;;IAE5Ed,EAAA,CAAAC,cAAA,aAC8C;IADPD,EAAA,CAAAE,UAAA,mBAAA+C,4DAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,kBAAkB,CAAC;IAAA,EAAC;IAE3ET,EAAA,CAAAU,MAAA,yBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAsC;IAChFV,EADgF,CAAAW,YAAA,EAAO,EAClF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,qBAAsC;;;;;;IAEhFd,EAAA,CAAAC,cAAA,aAA2G;IAApED,EAAA,CAAAE,UAAA,mBAAAiD,4DAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,UAAU,CAAC;IAAA,EAAC;IACnET,EAAA,CAAAU,MAAA,iBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAA8B;IACxEV,EADwE,CAAAW,YAAA,EAAO,EAC1E;;;;IADqCX,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,aAA8B;;;;;;IAExEd,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAAmD,4DAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAqC;IAC/EV,EAD+E,CAAAW,YAAA,EAAO,EACjF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAAqC;;;;;;IAE/Ed,EAAA,CAAAC,cAAA,aAA8G;IAAvED,EAAA,CAAAE,UAAA,mBAAAqD,4DAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,YAAY,CAAC;IAAA,EAAC;IACrET,EAAA,CAAAU,MAAA,mBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAgC;IAC1EV,EAD0E,CAAAW,YAAA,EAAO,EAC5E;;;;IADqCX,EAAA,CAAAY,SAAA,GAAgC;IAAhCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,eAAgC;;;;;;IAE1Ed,EAAA,CAAAC,cAAA,aAC0C;IADHD,EAAA,CAAAE,UAAA,mBAAAuD,4DAAA;MAAAzD,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,cAAc,CAAC;IAAA,EAAC;IAEvET,EAAA,CAAAU,MAAA,qBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAkC;IAC5EV,EAD4E,CAAAW,YAAA,EAAO,EAC9E;;;;IADqCX,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,iBAAkC;;;;;;IAE5Ed,EAAA,CAAAC,cAAA,aAC+C;IADRD,EAAA,CAAAE,UAAA,mBAAAyD,4DAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,mBAAmB,CAAC;IAAA,EAAC;IAE5ET,EAAA,CAAAU,MAAA,0BACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAAuC;IACjFV,EADiF,CAAAW,YAAA,EAAO,EACnF;;;;IADqCX,EAAA,CAAAY,SAAA,GAAuC;IAAvCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,sBAAuC;;;;;IAIjFd,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAU,MAAA,kBACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IACLX,EAAA,CAAAC,cAAA,aAAqF;IAAAD,EAAA,CAAAU,MAAA,cAAO;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAO/FX,EADF,CAAAC,cAAA,SAAqC,eACwB;IACzDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAC,IAAA,MACF;;;;;IAGA/D,EADF,CAAAC,cAAA,SAAqC,eACmB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAE,IAAA,kBAAAF,YAAA,CAAAE,IAAA,CAAAC,OAAA,MACF;;;;;IAGAjE,EADF,CAAAC,cAAA,SAAqC,eACmB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAI,IAAA,kBAAAJ,YAAA,CAAAI,IAAA,CAAAD,OAAA,MACF;;;;;;IAGAjE,EADF,CAAAC,cAAA,SAAyC,iBAEO;IAA5CD,EAAA,CAAAE,UAAA,mBAAAiE,qEAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAAgE,IAAA;MAAA,MAAAN,YAAA,GAAA9D,EAAA,CAAAO,aAAA,GAAA8D,SAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgE,cAAA,CAAAR,YAAA,CAAAS,QAAA,CAAiC;IAAA,EAAC;IAC3CvE,EAAA,CAAAwE,SAAA,YAA4C;IAEhDxE,EADE,CAAAW,YAAA,EAAS,EACN;;;;;IAIHX,EADF,CAAAC,cAAA,SAA6C,eACW;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAW,YAAA,MACF;;;;;IAGAzE,EADF,CAAAC,cAAA,SAAyC,eACe;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAY,QAAA,MACF;;;;;IAGA1E,EADF,CAAAC,cAAA,SAA+C,eACS;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAa,cAAA,MACF;;;;;IAGA3E,EADF,CAAAC,cAAA,SAA2C,eACa;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAc,UAAA,MACF;;;;;IAGA5E,EADF,CAAAC,cAAA,SAAsC,eACkB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAe,KAAA,MACF;;;;;IAGA7E,EADF,CAAAC,cAAA,SAAyC,eACe;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAgB,QAAA,cACF;;;;;IAGA9E,EADF,CAAAC,cAAA,SAA2C,eACa;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAiB,UAAA,cACF;;;;;IAGA/E,EADF,CAAAC,cAAA,SAA6C,eACW;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAkB,YAAA,cACF;;;;;IAGAhF,EADF,CAAAC,cAAA,SAA8C,eACU;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAmB,aAAA,MACF;;;;;IAGAjF,EADF,CAAAC,cAAA,SAAkD,eACM;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAoB,iBAAA,MACF;;;;;IAGAlF,EADF,CAAAC,cAAA,SAA+C,eACS;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAqB,cAAA,MACF;;;;;IAGAnF,EADF,CAAAC,cAAA,SAAqC,eACmB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAsB,IAAA,MACF;;;;;IAGApF,EADF,CAAAC,cAAA,SAA8C,eACU;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAuB,aAAA,MACF;;;;;IAGArF,EADF,CAAAC,cAAA,SAA+C,eACL;IAAAD,EAAA,CAAAU,MAAA,GAA6B;IACvEV,EADuE,CAAAW,YAAA,EAAO,EACzE;;;;IADqCX,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAAa,iBAAA,CAAAiD,YAAA,CAAAwB,cAAA,CAA6B;;;;;IAGrEtF,EADF,CAAAC,cAAA,SAA4C,eACL;IAAAD,EAAA,CAAAU,MAAA,GAA0B;IACjEV,EADiE,CAAAW,YAAA,EAAO,EACnE;;;;IADkCX,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAa,iBAAA,CAAAiD,YAAA,CAAAyB,WAAA,CAA0B;;;;;IAG/DvF,EADF,CAAAC,cAAA,SAAgD,eACN;IAAAD,EAAA,CAAAU,MAAA,GAA8B;IACxEV,EADwE,CAAAW,YAAA,EAAO,EAC1E;;;;IADqCX,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAa,iBAAA,CAAAiD,YAAA,CAAA0B,eAAA,CAA8B;;;;;IAGtExF,EADF,CAAAC,cAAA,SAAyC,eACe;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAA2B,QAAA,MACF;;;;;IAGAzF,EADF,CAAAC,cAAA,SAA+C,eACL;IAAAD,EAAA,CAAAU,MAAA,GAA6B;IACvEV,EADuE,CAAAW,YAAA,EAAO,EACzE;;;;IADqCX,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAAa,iBAAA,CAAAiD,YAAA,CAAA4B,cAAA,CAA6B;;;;;IAGrE1F,EADF,CAAAC,cAAA,SAA0C,eACa;IACnDD,EAAA,CAAAU,MAAA,GACF;;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAA7D,EAAA,CAAA2F,WAAA,OAAA7B,YAAA,CAAA8B,SAAA,iCACF;;;;;IAGA5F,EADF,CAAAC,cAAA,SAA4C,eACW;IACnDD,EAAA,CAAAU,MAAA,GACF;;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAA7D,EAAA,CAAA2F,WAAA,OAAA7B,YAAA,CAAA+B,WAAA,iCACF;;;;;IAGA7F,EADF,CAAAC,cAAA,SAAiD,eACO;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA6D,kBAAA,MAAAC,YAAA,CAAAgC,gBAAA,MACF;;;;;;IAKA9F,EADF,CAAAC,cAAA,SAAyC,iBACiD;IAA9CD,EAAA,CAAAE,UAAA,mBAAA6F,sEAAA;MAAA/F,EAAA,CAAAI,aAAA,CAAA4F,IAAA;MAAA,MAAAlC,YAAA,GAAA9D,EAAA,CAAAO,aAAA,GAAA8D,SAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2F,iBAAA,CAAAnC,YAAA,CAAAoC,OAAA,CAAmC;IAAA,EAAC;IACrFlG,EAAA,CAAAwE,SAAA,YAA2C;IAACxE,EAAA,CAAAU,MAAA,kBAC9C;IACFV,EADE,CAAAW,YAAA,EAAS,EACN;;;;;;IAhIPX,EAAA,CAAAC,cAAA,SAAkC;IA4HhCD,EA1HA,CAAAmG,UAAA,IAAAC,4CAAA,iBAAqC,IAAAC,4CAAA,iBAKA,IAAAC,4CAAA,iBAKA,IAAAC,4CAAA,iBAKI,IAAAC,4CAAA,iBAQI,IAAAC,4CAAA,iBAKJ,IAAAC,4CAAA,iBAKM,IAAAC,4CAAA,iBAKJ,IAAAC,4CAAA,iBAKL,KAAAC,6CAAA,iBAKG,KAAAC,6CAAA,iBAKE,KAAAC,6CAAA,iBAKE,KAAAC,6CAAA,iBAKC,KAAAC,6CAAA,iBAKI,KAAAC,6CAAA,iBAKH,KAAAC,6CAAA,iBAKV,KAAAC,6CAAA,iBAKS,KAAAC,6CAAA,iBAKC,KAAAC,6CAAA,iBAGH,KAAAC,6CAAA,iBAGI,KAAAC,6CAAA,iBAGP,KAAAC,6CAAA,iBAKM,KAAAC,6CAAA,iBAGL,KAAAC,6CAAA,iBAKE,KAAAC,6CAAA,iBAKK,KAAAC,6CAAA,iBAOR;IAOrC7H,EAFJ,CAAAC,cAAA,cAA0B,eACF,kBAEQ;IAC1BD,EAAA,CAAAwE,SAAA,aAA6C;IAC/CxE,EAAA,CAAAW,YAAA,EAAS;IAGLX,EAFJ,CAAAC,cAAA,cAA0B,UACpB,kBAC6D;IAAjCD,EAAA,CAAAE,UAAA,mBAAA4H,iEAAA;MAAA,MAAAhE,YAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAA2H,IAAA,EAAA1D,SAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0H,YAAA,CAAAlE,YAAA,CAAsB;IAAA,EAAC;IAC5D9D,EAAA,CAAAwE,SAAA,aAAoC;IAACxE,EAAA,CAAAU,MAAA,2BACvC;IAKVV,EALU,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACH,EACF;;;;IA9IEX,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,SAA8B;IAK9BlI,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,SAA8B;IAK9BlI,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,SAA8B;IAK9BlI,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,aAAkC;IAQlClI,EAAA,CAAAY,SAAA,EAAsC;IAAtCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,iBAAsC;IAKtClI,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,aAAkC;IAKlClI,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,mBAAwC;IAKxClI,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,eAAoC;IAKpClI,EAAA,CAAAY,SAAA,EAA+B;IAA/BZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,UAA+B;IAK/BlI,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,aAAkC;IAKlClI,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,eAAoC;IAKpClI,EAAA,CAAAY,SAAA,EAAsC;IAAtCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,iBAAsC;IAKtClI,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,kBAAuC;IAKvClI,EAAA,CAAAY,SAAA,EAA2C;IAA3CZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,sBAA2C;IAK3ClI,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,mBAAwC;IAKxClI,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,SAA8B;IAK9BlI,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,kBAAuC;IAKvClI,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,mBAAwC;IAGxClI,EAAA,CAAAY,SAAA,EAAqC;IAArCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,gBAAqC;IAGrClI,EAAA,CAAAY,SAAA,EAAyC;IAAzCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,oBAAyC;IAGzClI,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,aAAkC;IAKlClI,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,mBAAwC;IAGxClI,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,cAAmC;IAKnClI,EAAA,CAAAY,SAAA,EAAqC;IAArCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,gBAAqC;IAKrClI,EAAA,CAAAY,SAAA,EAA0C;IAA1CZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,qBAA0C;IAO1ClI,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAAiI,UAAA,SAAA3H,MAAA,CAAA4H,gBAAA,aAAkC;;;AD/O/C,OAAM,MAAOC,wBAAyB,SAAQrI,iBAAiB;EAajDsI,EAAA;EACAC,WAAA;EACFC,SAAA;EACAC,MAAA;EAdV;EACAC,QAAQ;EAECC,cAAc;EACvBC,aAAa,GAAkB,IAAI;EACnCC,gBAAgB,GAA2B,IAAI;EAE/C;EACAC,cAAc,GAAa,EAAE;EAE7BC,YACYT,EAAqB,EACrBC,WAAwB,EAC1BC,SAAuB,EACvBC,MAAc;IAEtB,KAAK,CAACH,EAAE,CAAC;IALC,KAAAA,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACb,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IAGd,MAAMO,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAIC,IAAI,GAAGH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,GAAG,IAAI;IACjD,IAAI,CAACN,QAAQ,GAAGS,IAAI,EAAET,QAAQ;IAC9B,IAAI,CAACY,UAAU,CAACf,WAAW,CAAC;IAC5B,IAAI,CAACgB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB,IAAI,CAACC,IAAI,CAACC,OAAO,GAAG;MAAEhB,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE;EACjD;EAEAiB,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACjB,cAAc,IAAI,CAACiB,OAAO,CAACjB,cAAc,CAACkB,WAAW,EAAE;MACjE,IAAI,CAACJ,IAAI,CAACC,OAAO,GAAG;QAAEhB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAE,GAAG,IAAI,CAACC;MAAc,CAAC;MACtE,IAAI,CAACmB,oBAAoB,EAAE;MAC3B,IAAI,CAACC,WAAW,CAAC,IAAI,CAACN,IAAI,CAAC;IAC7B;EACF;EAEAO,QAAQA,CAAA;IACN,KAAK,CAACA,QAAQ,EAAE;IAChB,IAAI,CAACF,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACnB,cAAc,EAAEsB,YAAY,IAAI,IAAI,CAACtB,cAAc,EAAEuB,YAAY,EAAE;MAC1E,IAAI,CAACpB,cAAc,GAAG,IAAI,CAACqB,eAAe,EAAE;IAC9C,CAAC,MAAM;MACL;MACA,IAAI,CAACrB,cAAc,GAAG,CACpB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EACrE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,UAAU,EAAE,kBAAkB,CAC1E;IACH;EACF;EAEA;EACAqB,eAAeA,CAAA;IACb,MAAMF,YAAY,GAAG,IAAI,CAACtB,cAAc,EAAEsB,YAAY;IACtD,MAAMhG,IAAI,GAAG,IAAI,CAAC0E,cAAc,EAAEuB,YAAY;IAE9C;IACA,MAAME,WAAW,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC;IACxD,MAAMC,aAAa,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;IAE7C,IAAIC,eAAe,GAAa,EAAE;IAElC,IAAIL,YAAY,KAAK,kBAAkB,KAAKhG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,UAAU,CAAC,EAAE;MACnLqG,eAAe,GAAG,CAAC,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,CAAC;IAC3M,CAAC,MACI,IAAIL,YAAY,KAAK,kBAAkB,KAAKhG,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,gBAAgB,CAAC,EAAE;MAC9FqG,eAAe,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,kBAAkB,CAAC;IACjM,CAAC,MACI,IAAIL,YAAY,KAAK,kBAAkB,KAAKhG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAC,EAAE;MACxKqG,eAAe,GAAG,CAAC,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;IACtM,CAAC,MACI,IAAIL,YAAY,KAAK,kBAAkB,KAAKhG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,CAAC,EAAE;MAC/FqG,eAAe,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,iBAAiB,EAAE,aAAa,EAAE,kBAAkB,CAAC;IACzK,CAAC,MACI,IAAIL,YAAY,KAAK,kBAAkB,KAAKhG,IAAI,KAAK,yBAAyB,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,iCAAiC,IAAIA,IAAI,KAAK,kBAAkB,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,kBAAkB,CAAC,EAAE;MACvVqG,eAAe,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;IAC1L,CAAC,MACI,IAAIL,YAAY,KAAK,iBAAiB,KAAKhG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,SAAS,CAAC,EAAE;MAClKqG,eAAe,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,CAAC;IAClO,CAAC,MACI,IAAIL,YAAY,KAAK,iBAAiB,KAAKhG,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,CAAC,EAAE;MACjIqG,eAAe,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,CAAC;IAC/O,CAAC,MACI,IAAIL,YAAY,KAAK,iBAAiB,KAAKhG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAC,EAAE;MACvKqG,eAAe,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,CAAC;IAC3N,CAAC,MACI,IAAIL,YAAY,KAAK,SAAS,KAAKhG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,gBAAgB,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,SAAS,CAAC,EAAE;MACjWqG,eAAe,GAAG,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;IAC5O,CAAC,MACI,IAAIL,YAAY,KAAK,SAAS,KAAKhG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,YAAY,CAAC,EAAE;MAChNqG,eAAe,GAAG,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,CAAC;IAC1O;IAEA,OAAO,CAAC,GAAGF,WAAW,EAAE,GAAGE,eAAe,EAAE,GAAGD,aAAa,CAAC;EAC/D;EAEA;EACAjC,gBAAgBA,CAACmC,UAAkB;IACjC,OAAO,IAAI,CAACzB,cAAc,CAAC0B,QAAQ,CAACD,UAAU,CAAC;EACjD;EAEA/F,cAAcA,CAACC,QAAgB;IAC7B,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACgG,IAAI,EAAE,KAAK,EAAE,EAAE;MACvCxK,IAAI,CAACyK,IAAI,CAAC;QACRC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,uBAAuB;QAC7BC,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACpB,CAAC;MACF;IACF;IACA,IACErG,QAAQ,CAAC+F,QAAQ,CAAC,iBAAiB,CAAC,IACpC/F,QAAQ,CAAC+F,QAAQ,CAAC,iBAAiB,CAAC,EACpC;MACAO,MAAM,CAACC,IAAI,CAACvG,QAAQ,EAAE,QAAQ,CAAC;MAC/B;IACF;IAEA,MAAMwG,MAAM,GAAG,mDAAmDC,kBAAkB,CAClFzG,QAAQ,CACT,EAAE;IACHsG,MAAM,CAACC,IAAI,CAACC,MAAM,EAAE,QAAQ,CAAC;EAC/B;EAEA;EAECE,qBAAqB,GAAkB,IAAI;EAE5ChF,iBAAiBA,CAACiF,OAAe;IAC/B,IAAI,CAACD,qBAAqB,GAAGC,OAAO;IAEpC,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC;IACjE,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAIzL,KAAK,CAACsL,YAAY,CAAC;MACrCG,KAAK,CAACC,IAAI,EAAE;IACd;EACF;EAEAvD,YAAYA,CAACK,WAAgB;IAC3B,IAAI,CAACE,MAAM,CAACiD,QAAQ,CAAC,CAAC,0CAA0C,CAAC,EAAE;MACjEC,WAAW,EAAE;QAAEC,MAAM,EAAErD,WAAW,CAACsD;MAAE;KACtC,CAAC;EAEJ;;qCAlJWxD,wBAAwB,EAAAnI,EAAA,CAAA4L,iBAAA,CAAA5L,EAAA,CAAA6L,iBAAA,GAAA7L,EAAA,CAAA4L,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAA/L,EAAA,CAAA4L,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAAjM,EAAA,CAAA4L,iBAAA,CAAAM,EAAA,CAAAC,MAAA;EAAA;;UAAxBhE,wBAAwB;IAAAiE,SAAA;IAAAC,MAAA;MAAA5D,cAAA;IAAA;IAAA6D,QAAA,GAAAtM,EAAA,CAAAuM,0BAAA,EAAAvM,EAAA,CAAAwM,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCX/B9M,EAHN,CAAAC,cAAA,aAAmC,eACsD,YAC9E,YAC2D;QA0H9DD,EAxHA,CAAAmG,UAAA,IAAA6G,sCAAA,gBACmC,IAAAC,sCAAA,gBAImE,IAAAC,sCAAA,gBAIA,IAAAC,sCAAA,gBAIzC,IAAAC,sCAAA,gBAMlB,IAAAC,sCAAA,gBAIiE,KAAAC,uCAAA,gBAK/D,KAAAC,uCAAA,gBAImE,KAAAC,uCAAA,gBAIX,KAAAC,uCAAA,gBAIO,KAAAC,uCAAA,gBAII,KAAAC,uCAAA,gBAKrE,KAAAC,uCAAA,gBAKC,KAAAC,uCAAA,gBAKI,KAAAC,uCAAA,gBAKH,KAAAC,uCAAA,gBAIsD,KAAAC,uCAAA,gBAKvD,KAAAC,uCAAA,gBAKC,KAAAC,uCAAA,gBAKH,KAAAC,uCAAA,gBAKI,KAAAC,uCAAA,gBAI6D,KAAAC,uCAAA,gBAK9D,KAAAC,uCAAA,gBAIiE,KAAAC,uCAAA,gBAKpE,KAAAC,uCAAA,gBAKK,KAAAC,uCAAA,gBAMc,KAAAC,uCAAA,gBAGwB;QAEzF1O,EADE,CAAAW,YAAA,EAAK,EACC;QACRX,EAAA,CAAAC,cAAA,aAAO;QACLD,EAAA,CAAAmG,UAAA,KAAAwI,uCAAA,kBAAkC;QAkJtC3O,EADE,CAAAW,YAAA,EAAQ,EACF;QAENX,EADF,CAAAC,cAAA,cAAiB,0BAEuB;QAApCD,EAAA,CAAAE,UAAA,wBAAA0O,wEAAAC,MAAA;UAAA,OAAc9B,GAAA,CAAA+B,YAAA,CAAAD,MAAA,CAAoB;QAAA,EAAC;QAGzC7O,EAFI,CAAAW,YAAA,EAAiB,EACb,EACF;QAENX,EAAA,CAAAwE,SAAA,oCAAqG;;;QArR1FxE,EAAA,CAAAY,SAAA,GAA8B;QAA9BZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,SAA8B;QAIqClI,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,SAA8B;QAI9BlI,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,SAA8B;QAI3ElI,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,aAAkC;QAMxDlI,EAAA,CAAAY,SAAA,EAAsC;QAAtCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,iBAAsC;QAI+BlI,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,aAAkC;QAKvGlI,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,mBAAwC;QAI+BlI,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,eAAoC;QAI1ClI,EAAA,CAAAY,SAAA,EAA+B;QAA/BZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,UAA+B;QAI3BlI,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,aAAkC;QAIhClI,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,eAAoC;QAK3GlI,EAAA,CAAAY,SAAA,EAAsC;QAAtCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,iBAAsC;QAKtClI,EAAA,CAAAY,SAAA,EAAuC;QAAvCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,kBAAuC;QAKvClI,EAAA,CAAAY,SAAA,EAA2C;QAA3CZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,sBAA2C;QAK3ClI,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,mBAAwC;QAIwBlI,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,SAA8B;QAK9FlI,EAAA,CAAAY,SAAA,EAAuC;QAAvCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,kBAAuC;QAKvClI,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,mBAAwC;QAKxClI,EAAA,CAAAY,SAAA,EAAqC;QAArCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,gBAAqC;QAKrClI,EAAA,CAAAY,SAAA,EAAyC;QAAzCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,oBAAyC;QAI2BlI,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,aAAkC;QAKtGlI,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,mBAAwC;QAI8BlI,EAAA,CAAAY,SAAA,EAAmC;QAAnCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,cAAmC;QAKzGlI,EAAA,CAAAY,SAAA,EAAqC;QAArCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,gBAAqC;QAKrClI,EAAA,CAAAY,SAAA,EAA0C;QAA1CZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,qBAA0C;QAMpBlI,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,aAAkC;QAGTlI,EAAA,CAAAY,SAAA,EAAiC;QAAjCZ,EAAA,CAAAiI,UAAA,SAAA8E,GAAA,CAAA7E,gBAAA,YAAiC;QAI5DlI,EAAA,CAAAY,SAAA,GAAO;QAAPZ,EAAA,CAAAiI,UAAA,YAAA8E,GAAA,CAAAgC,IAAA,CAAO;QAoJlB/O,EAAA,CAAAY,SAAA,GAAiC;QAA4BZ,EAA7D,CAAAiI,UAAA,eAAA8E,GAAA,CAAAxD,IAAA,CAAAyF,aAAA,CAAiC,iBAAAjC,GAAA,CAAAxD,IAAA,CAAA0F,IAAA,CAA2B,gBAAAlC,GAAA,CAAAxD,IAAA,CAAA2F,UAAA,CAAgC;QAMtFlP,EAAA,CAAAY,SAAA,EAA+C;QAA/CZ,EAAA,CAAAiI,UAAA,0BAAA8E,GAAA,CAAA9B,qBAAA,CAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}