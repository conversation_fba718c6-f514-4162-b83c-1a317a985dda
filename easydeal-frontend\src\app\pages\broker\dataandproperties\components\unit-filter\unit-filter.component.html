<div class="filter-dropdown">
  <div class="mb-2">
    <label class="form-label">Finishing Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">select</option>
      <option *ngFor="let type of finishingTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.status">
      <option value="">Select</option>
      <option *ngFor="let state of status" [value]="state.value">{{ state.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">Unit Type:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.unitType">
      <option value="">Select</option>
      <option *ngFor="let unit of unitTypes" [value]="unit.value">{{ unit.key}}</option>
    </select>
  </div>

  <button class="btn btn-sm btn-primary w-100" (click)="apply()">Apply Filters</button>
</div>