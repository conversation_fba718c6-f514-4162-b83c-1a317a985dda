import { ChangeDetectorRef, Component, Input, SimpleChanges } from '@angular/core';
import { Modal } from 'bootstrap';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { UnitService } from '../../../services/unit.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';


@Component({
  selector: 'app-propertiestable',
  templateUrl: './propertiestable.component.html',
  styleUrl: './propertiestable.component.scss',
})
export class PropertiestableComponent extends BaseGridComponent {

  //session
  brokerId: number;

  @Input() appliedFilters: any;
  @Input() dynamicFilters: any;
  selectedImage: string | null = null;
  selectedLocation: SafeResourceUrl | null = null;

  constructor(
    protected cd: ChangeDetectorRef,
    protected unitService: UnitService,
    private sanitizer: DomSanitizer,
    private router: Router
  ) {
    super(cd);
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = user?.brokerId;
    this.setService(unitService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.page.filters = { brokerId: this.brokerId };
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.appliedFilters && !changes.appliedFilters.firstChange) {
      this.page.filters = { brokerId: this.brokerId, ...this.appliedFilters}
      this.reloadTable(this.page);
    }
    if (changes.dynamicFilters && !changes.dynamicFilters.firstChange) {
      // Dynamic filters don't affect the API call, just the table display
      this.cd.detectChanges();
    }
  }

  showImageModal(location: string) {
    if (!location || location.trim() === '') {
      Swal.fire({
        title: 'Warning',
        text: 'No location available',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      return;
    }
    if (
      location.includes('maps.google.com') ||
      location.includes('maps.app.goo.gl')
    ) {
      window.open(location, '_blank');
      return;
    }

    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
      location
    )}`;
    window.open(mapUrl, '_blank');
  }

  //****************************** */

   selectedUnitPlanImage: string | null = null;

  showUnitPlanModal(imgPath: string) {
    this.selectedUnitPlanImage = imgPath;

    const modalElement = document.getElementById('viewUnitPlanModal');
    if (modalElement) {
      const modal = new Modal(modalElement);
      modal.show();
    }
  }

  viewProperty(unitService: any) {
    this.router.navigate(['/developer/projects/models/units/details'], {
      queryParams: { unitId: unitService.id }
    });
  }

  getFieldsToShow(): string[] {
    if (!this.dynamicFilters) {
      return ['type', 'city', 'area', 'location', 'buildingNumber', 'view', 'floor', 'deliveryDate', 'finishingType', 'status', 'unitPlan', 'otherAccessories', 'actions'];
    }

    const compoundType = this.dynamicFilters.compoundType;
    const type = this.dynamicFilters.propertyType;

    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {
      return ['type', 'city', 'area', 'location', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'outside_compound' && (type === 'villas' || type === 'full_buildings')) {
      return ['type', 'city', 'area', 'location', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {
      return ['type', 'city', 'area', 'location', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {
      return ['type', 'city', 'area', 'location', 'buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {
      return ['type', 'city', 'area', 'location', 'unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {
      return ['type', 'city', 'area', 'location', 'compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {
      return ['type', 'city', 'area', 'location', 'compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {
      return ['type', 'city', 'area', 'location', 'compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment', 'actions'];
    }
    else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {
      return ['type', 'city', 'area', 'location', 'buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent', 'actions'];
    }
    else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {
      return ['type', 'city', 'area', 'location', 'buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent', 'actions'];
    }

    return ['type', 'city', 'area', 'location', 'buildingNumber', 'view', 'floor', 'deliveryDate', 'finishingType', 'status', 'unitPlan', 'otherAccessories', 'actions'];
  }

  shouldShowField(fieldName: string): boolean {
    return this.getFieldsToShow().includes(fieldName);
  }
//************************************** */

  //  sortData(column: string) {
  //    if (this.orderBy === column) {
  //     this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
  //   } else {
  //     this.orderBy = column;
  //     this.orderDir = 'asc';
  //   }

  //    this.page.orderBy = this.orderBy;
  //   this.page.orderDir = this.orderDir;
  //   this.page.pageNumber = 0;
  //   this.reloadTable(this.page);
  // }

  //  getSortArrow(column: string): string {
  //   if (this.orderBy !== column) {
  //     return '';
  //   }
  //   return this.orderDir === 'asc' ? '↑' : '↓';
  // }
}
