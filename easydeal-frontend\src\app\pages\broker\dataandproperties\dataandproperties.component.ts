import { ChangeDetectorRef, Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UnitService } from '../services/unit.service';
import { saveAs } from 'file-saver';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dataandproperties',
  templateUrl: './dataandproperties.component.html',
  styleUrl: './dataandproperties.component.scss',
})
export class DataandpropertiesComponent implements OnInit {
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;

  showEmptyCard = false;
  showSuccessCard = false;
  showPublishCard = false;
  isFilterDropdownVisible = false;

  brokerId :any;
  user: any;

  properties: any[] = [];
  isLoading = false;
  appliedFilters: any = {};
  dynamicFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;

  constructor(
    private unitService: UnitService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef
  ) {}

  ngOnInit() {
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;
    this.brokerId= this.user?.brokerId;
    this.checkRouteParams();
    this.loadPropertiesCount();
  }

  loadPropertiesCount() {
    this.isLoading = true;
    this.unitService.getByBrokerId(this.brokerId).subscribe({
      next: (response: any) => {
        this.properties = response.data || [];
        this.isLoading = false;
        this.updateCardVisibility();
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading properties:', error);
        this.properties = [];
        this.isLoading = false;
        this.updateCardVisibility();
        this.cd.detectChanges();
      },
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.appliedFilters = {
        ...this.appliedFilters,
        unitType: value.trim(),
      };
      this.cd.detectChanges();
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    console.log('Received filters:', filters);
    this.toggleFilterDropdown();
    this.appliedFilters = filters;
    this.cd.detectChanges();
  }

  checkRouteParams() {
    this.route.queryParams.subscribe((params) => {
      if (params['success'] === 'add') {
        this.showSuccessCard = true;
        this.hideCardsAfterDelay();
      } else if (params['success'] === 'publish') {
        this.showPublishCard = true;
        this.hideCardsAfterDelay();
      }
    });
  }

  updateCardVisibility() {
    this.showEmptyCard =
      this.properties.length === 0 &&
      !this.showSuccessCard &&
      !this.showPublishCard;
  }

  hideCardsAfterDelay() {
    setTimeout(() => {
      this.showSuccessCard = false;
      this.showPublishCard = false;
      this.updateCardVisibility();
    }, 5000);
  }

  onBackToTable() {
    this.showSuccessCard = false;
    this.showPublishCard = false;
    this.updateCardVisibility();
    this.cd.detectChanges();
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      console.log('File selected:', file.name);
      this.handleFileUpload(file);
    }
  }

  handleFileUpload(file: File) {
    console.log('Uploading file:', file.name);
    this.unitService.uploadExcelUnits(file, this.brokerId).subscribe({
      next: async (response) => {
        console.log('Upload successful:', response);
        this.showEmptyCard = false;
        this.appliedFilters = {
          ...this.appliedFilters,
          refreshTimestamp: Date.now(),
        };
        this.loadPropertiesCount();
        this.cd.detectChanges();
        // Reset file input
        if (this.fileInput && this.fileInput.nativeElement) {
          this.fileInput.nativeElement.value = '';
        }
        this.showSuccessCard = true;
        this.hideCardsAfterDelay();
      },
      error: (error) => {
        console.error('Upload error:', error);
        Swal.fire('Error', 'Error uploading file. Please try again.', 'error');
      },
    });
  }

  downloadTemplate() {
    this.unitService.downloadExcelTemplate().subscribe({
      next: (blob: Blob) => {
        saveAs(blob, 'units-template.xlsx');
      },
      error: (err) => console.error('Download error:', err),
    });
  }
}
