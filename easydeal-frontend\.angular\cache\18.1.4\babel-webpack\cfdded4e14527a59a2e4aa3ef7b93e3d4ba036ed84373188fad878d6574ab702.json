{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { saveAs } from 'file-saver';\nimport Swal from 'sweetalert2';\nlet DataandpropertiesComponent = class DataandpropertiesComponent {\n  unitService;\n  route;\n  cd;\n  fileInput;\n  showEmptyCard = false;\n  showSuccessCard = false;\n  showPublishCard = false;\n  isFilterDropdownVisible = false;\n  brokerId;\n  user;\n  properties = [];\n  isLoading = false;\n  appliedFilters = {};\n  searchText = '';\n  searchTimeout;\n  // Filter properties\n  currentCompoundType = '';\n  currentPropertyType = '';\n  activeFilterButtons = [];\n  constructor(unitService, route, cd) {\n    this.unitService = unitService;\n    this.route = route;\n    this.cd = cd;\n  }\n  ngOnInit() {\n    const userJson = localStorage.getItem('currentUser');\n    this.user = userJson ? JSON.parse(userJson) : null;\n    this.brokerId = this.user?.brokerId;\n    this.checkRouteParams();\n    this.loadPropertiesCount();\n  }\n  loadPropertiesCount() {\n    this.isLoading = true;\n    this.unitService.getByBrokerId(this.brokerId).subscribe({\n      next: response => {\n        this.properties = response.data || [];\n        this.isLoading = false;\n        this.updateCardVisibility();\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading properties:', error);\n        this.properties = [];\n        this.isLoading = false;\n        this.updateCardVisibility();\n        this.cd.detectChanges();\n      }\n    });\n  }\n  onSearchTextChange(value) {\n    clearTimeout(this.searchTimeout);\n    this.searchTimeout = setTimeout(() => {\n      this.appliedFilters = {\n        ...this.appliedFilters,\n        unitType: value.trim()\n      };\n      this.cd.detectChanges();\n    }, 300);\n  }\n  toggleFilterDropdown() {\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\n  }\n  onFiltersApplied(filters) {\n    console.log('Received filters:', filters);\n    this.toggleFilterDropdown();\n    this.appliedFilters = filters;\n    this.cd.detectChanges();\n  }\n  // Filter button methods\n  applyFilter(compoundType, propertyType) {\n    this.currentCompoundType = compoundType;\n    this.currentPropertyType = propertyType;\n    const filterKey = `${compoundType}_${propertyType}`;\n    // Update active filter buttons\n    this.activeFilterButtons = [filterKey];\n    // Apply filters to table\n    this.appliedFilters = {\n      ...this.appliedFilters,\n      compoundType: compoundType,\n      propertyType: propertyType,\n      filterKey: filterKey\n    };\n    this.cd.detectChanges();\n  }\n  clearFilters() {\n    this.currentCompoundType = '';\n    this.currentPropertyType = '';\n    this.activeFilterButtons = [];\n    // Remove compound and property type filters but keep other filters\n    const {\n      compoundType,\n      propertyType,\n      filterKey,\n      ...otherFilters\n    } = this.appliedFilters;\n    this.appliedFilters = otherFilters;\n    this.cd.detectChanges();\n  }\n  isFilterActive(compoundType, propertyType) {\n    const filterKey = `${compoundType}_${propertyType}`;\n    return this.activeFilterButtons.includes(filterKey);\n  }\n  // Get fields to show based on compound type and property type\n  getFieldsToShow() {\n    const compoundType = this.currentCompoundType;\n    const type = this.currentPropertyType;\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\n      return ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\n      return ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      return ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\n      return ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\n      return ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\n      return ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\n      return ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      return ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'];\n    } else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];\n    }\n    return [];\n  }\n  // Check if a specific field should be shown\n  shouldShowField(fieldName) {\n    return this.getFieldsToShow().includes(fieldName);\n  }\n  checkRouteParams() {\n    this.route.queryParams.subscribe(params => {\n      if (params['success'] === 'add') {\n        this.showSuccessCard = true;\n        this.hideCardsAfterDelay();\n      } else if (params['success'] === 'publish') {\n        this.showPublishCard = true;\n        this.hideCardsAfterDelay();\n      }\n    });\n  }\n  updateCardVisibility() {\n    this.showEmptyCard = this.properties.length === 0 && !this.showSuccessCard && !this.showPublishCard;\n  }\n  hideCardsAfterDelay() {\n    setTimeout(() => {\n      this.showSuccessCard = false;\n      this.showPublishCard = false;\n      this.updateCardVisibility();\n    }, 5000);\n  }\n  onBackToTable() {\n    this.showSuccessCard = false;\n    this.showPublishCard = false;\n    this.updateCardVisibility();\n    this.cd.detectChanges();\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      console.log('File selected:', file.name);\n      this.handleFileUpload(file);\n    }\n  }\n  handleFileUpload(file) {\n    var _this = this;\n    console.log('Uploading file:', file.name);\n    this.unitService.uploadExcelUnits(file, this.brokerId).subscribe({\n      next: function () {\n        var _ref = _asyncToGenerator(function* (response) {\n          console.log('Upload successful:', response);\n          _this.showEmptyCard = false;\n          _this.appliedFilters = {\n            ..._this.appliedFilters,\n            refreshTimestamp: Date.now()\n          };\n          _this.loadPropertiesCount();\n          _this.cd.detectChanges();\n          // Reset file input\n          if (_this.fileInput && _this.fileInput.nativeElement) {\n            _this.fileInput.nativeElement.value = '';\n          }\n          _this.showSuccessCard = true;\n          _this.hideCardsAfterDelay();\n        });\n        return function next(_x) {\n          return _ref.apply(this, arguments);\n        };\n      }(),\n      error: error => {\n        console.error('Upload error:', error);\n        Swal.fire('Error', 'Error uploading file. Please try again.', 'error');\n      }\n    });\n  }\n  downloadTemplate() {\n    this.unitService.downloadExcelTemplate().subscribe({\n      next: blob => {\n        saveAs(blob, 'units-template.xlsx');\n      },\n      error: err => console.error('Download error:', err)\n    });\n  }\n};\n__decorate([ViewChild('fileInput')], DataandpropertiesComponent.prototype, \"fileInput\", void 0);\nDataandpropertiesComponent = __decorate([Component({\n  selector: 'app-dataandproperties',\n  templateUrl: './dataandproperties.component.html',\n  styleUrl: './dataandproperties.component.scss'\n})], DataandpropertiesComponent);\nexport { DataandpropertiesComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "saveAs", "<PERSON><PERSON>", "DataandpropertiesComponent", "unitService", "route", "cd", "fileInput", "showEmptyCard", "showSuccessCard", "showPublishCard", "isFilterDropdownVisible", "brokerId", "user", "properties", "isLoading", "appliedFilters", "searchText", "searchTimeout", "currentCompoundType", "currentPropertyType", "activeFilterButtons", "constructor", "ngOnInit", "userJson", "localStorage", "getItem", "JSON", "parse", "checkRouteParams", "loadPropertiesCount", "getByBrokerId", "subscribe", "next", "response", "data", "updateCardVisibility", "detectChanges", "error", "console", "onSearchTextChange", "value", "clearTimeout", "setTimeout", "unitType", "trim", "toggleFilterDropdown", "onFiltersApplied", "filters", "log", "applyFilter", "compoundType", "propertyType", "<PERSON><PERSON><PERSON>", "clearFilters", "otherFilters", "isFilterActive", "includes", "getFieldsToShow", "type", "shouldShowField", "fieldName", "queryParams", "params", "hideCardsAfterDelay", "length", "onBackToTable", "onFileSelected", "event", "file", "target", "files", "name", "handleFileUpload", "_this", "uploadExcelUnits", "_ref", "_asyncToGenerator", "refreshTimestamp", "Date", "now", "nativeElement", "_x", "apply", "arguments", "fire", "downloadTemplate", "downloadExcelTemplate", "blob", "err", "__decorate", "selector", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\dataandproperties.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild, ElementRef } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { UnitService } from '../services/unit.service';\r\nimport { saveAs } from 'file-saver';\r\nimport Swal from 'sweetalert2';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-dataandproperties',\r\n  templateUrl: './dataandproperties.component.html',\r\n  styleUrl: './dataandproperties.component.scss',\r\n})\r\nexport class DataandpropertiesComponent implements OnInit {\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n  showEmptyCard = false;\r\n  showSuccessCard = false;\r\n  showPublishCard = false;\r\n  isFilterDropdownVisible = false;\r\n\r\n  brokerId :any;\r\n  user: any;\r\n\r\n  properties: any[] = [];\r\n  isLoading = false;\r\n  appliedFilters: any = {};\r\n  searchText: string = '';\r\n  private searchTimeout: any;\r\n\r\n  // Filter properties\r\n  currentCompoundType: string = '';\r\n  currentPropertyType: string = '';\r\n  activeFilterButtons: string[] = [];\r\n\r\n  constructor(\r\n    private unitService: UnitService,\r\n    private route: ActivatedRoute,\r\n    private cd: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    const userJson = localStorage.getItem('currentUser');\r\n    this.user = userJson ? JSON.parse(userJson) : null;\r\n    this.brokerId= this.user?.brokerId;\r\n    this.checkRouteParams();\r\n    this.loadPropertiesCount();\r\n  }\r\n\r\n  loadPropertiesCount() {\r\n    this.isLoading = true;\r\n    this.unitService.getByBrokerId(this.brokerId).subscribe({\r\n      next: (response: any) => {\r\n        this.properties = response.data || [];\r\n        this.isLoading = false;\r\n        this.updateCardVisibility();\r\n        this.cd.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading properties:', error);\r\n        this.properties = [];\r\n        this.isLoading = false;\r\n        this.updateCardVisibility();\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  onSearchTextChange(value: string): void {\r\n    clearTimeout(this.searchTimeout);\r\n    this.searchTimeout = setTimeout(() => {\r\n      this.appliedFilters = {\r\n        ...this.appliedFilters,\r\n        unitType: value.trim(),\r\n      };\r\n      this.cd.detectChanges();\r\n    }, 300);\r\n  }\r\n\r\n  toggleFilterDropdown() {\r\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\r\n  }\r\n\r\n  onFiltersApplied(filters: any) {\r\n    console.log('Received filters:', filters);\r\n    this.toggleFilterDropdown();\r\n    this.appliedFilters = filters;\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  // Filter button methods\r\n  applyFilter(compoundType: string, propertyType: string) {\r\n    this.currentCompoundType = compoundType;\r\n    this.currentPropertyType = propertyType;\r\n\r\n    const filterKey = `${compoundType}_${propertyType}`;\r\n\r\n    // Update active filter buttons\r\n    this.activeFilterButtons = [filterKey];\r\n\r\n    // Apply filters to table\r\n    this.appliedFilters = {\r\n      ...this.appliedFilters,\r\n      compoundType: compoundType,\r\n      propertyType: propertyType,\r\n      filterKey: filterKey\r\n    };\r\n\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  clearFilters() {\r\n    this.currentCompoundType = '';\r\n    this.currentPropertyType = '';\r\n    this.activeFilterButtons = [];\r\n\r\n    // Remove compound and property type filters but keep other filters\r\n    const { compoundType, propertyType, filterKey, ...otherFilters } = this.appliedFilters;\r\n    this.appliedFilters = otherFilters;\r\n\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  isFilterActive(compoundType: string, propertyType: string): boolean {\r\n    const filterKey = `${compoundType}_${propertyType}`;\r\n    return this.activeFilterButtons.includes(filterKey);\r\n  }\r\n\r\n  // Get fields to show based on compound type and property type\r\n  getFieldsToShow(): any[] {\r\n    const compoundType = this.currentCompoundType;\r\n    const type = this.currentPropertyType;\r\n\r\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\r\n      return ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\r\n      return ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\r\n      return ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\r\n      return ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\r\n      return ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\r\n      return ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\r\n      return ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\r\n      return ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {\r\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'];\r\n    }\r\n    else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {\r\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];\r\n    }\r\n\r\n    return [];\r\n  }\r\n\r\n  // Check if a specific field should be shown\r\n  shouldShowField(fieldName: string): boolean {\r\n    return this.getFieldsToShow().includes(fieldName);\r\n  }\r\n\r\n  checkRouteParams() {\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['success'] === 'add') {\r\n        this.showSuccessCard = true;\r\n        this.hideCardsAfterDelay();\r\n      } else if (params['success'] === 'publish') {\r\n        this.showPublishCard = true;\r\n        this.hideCardsAfterDelay();\r\n      }\r\n    });\r\n  }\r\n\r\n  updateCardVisibility() {\r\n    this.showEmptyCard =\r\n      this.properties.length === 0 &&\r\n      !this.showSuccessCard &&\r\n      !this.showPublishCard;\r\n  }\r\n\r\n  hideCardsAfterDelay() {\r\n    setTimeout(() => {\r\n      this.showSuccessCard = false;\r\n      this.showPublishCard = false;\r\n      this.updateCardVisibility();\r\n    }, 5000);\r\n  }\r\n\r\n  onBackToTable() {\r\n    this.showSuccessCard = false;\r\n    this.showPublishCard = false;\r\n    this.updateCardVisibility();\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      console.log('File selected:', file.name);\r\n      this.handleFileUpload(file);\r\n    }\r\n  }\r\n\r\n  handleFileUpload(file: File) {\r\n    console.log('Uploading file:', file.name);\r\n    this.unitService.uploadExcelUnits(file, this.brokerId).subscribe({\r\n      next: async (response) => {\r\n        console.log('Upload successful:', response);\r\n        this.showEmptyCard = false;\r\n        this.appliedFilters = {\r\n          ...this.appliedFilters,\r\n          refreshTimestamp: Date.now(),\r\n        };\r\n        this.loadPropertiesCount();\r\n        this.cd.detectChanges();\r\n        // Reset file input\r\n        if (this.fileInput && this.fileInput.nativeElement) {\r\n          this.fileInput.nativeElement.value = '';\r\n        }\r\n        this.showSuccessCard = true;\r\n        this.hideCardsAfterDelay();\r\n      },\r\n      error: (error) => {\r\n        console.error('Upload error:', error);\r\n        Swal.fire('Error', 'Error uploading file. Please try again.', 'error');\r\n      },\r\n    });\r\n  }\r\n\r\n  downloadTemplate() {\r\n    this.unitService.downloadExcelTemplate().subscribe({\r\n      next: (blob: Blob) => {\r\n        saveAs(blob, 'units-template.xlsx');\r\n      },\r\n      error: (err) => console.error('Download error:', err),\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAA4BA,SAAS,EAAUC,SAAS,QAAoB,eAAe;AAG3F,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAOC,IAAI,MAAM,aAAa;AAQvB,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAuB3BC,WAAA;EACAC,KAAA;EACAC,EAAA;EAxBcC,SAAS;EAEjCC,aAAa,GAAG,KAAK;EACrBC,eAAe,GAAG,KAAK;EACvBC,eAAe,GAAG,KAAK;EACvBC,uBAAuB,GAAG,KAAK;EAE/BC,QAAQ;EACRC,IAAI;EAEJC,UAAU,GAAU,EAAE;EACtBC,SAAS,GAAG,KAAK;EACjBC,cAAc,GAAQ,EAAE;EACxBC,UAAU,GAAW,EAAE;EACfC,aAAa;EAErB;EACAC,mBAAmB,GAAW,EAAE;EAChCC,mBAAmB,GAAW,EAAE;EAChCC,mBAAmB,GAAa,EAAE;EAElCC,YACUlB,WAAwB,EACxBC,KAAqB,EACrBC,EAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;EACT;EAEHiB,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAI,CAACb,IAAI,GAAGW,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAClD,IAAI,CAACZ,QAAQ,GAAE,IAAI,CAACC,IAAI,EAAED,QAAQ;IAClC,IAAI,CAACiB,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAACX,WAAW,CAAC2B,aAAa,CAAC,IAAI,CAACnB,QAAQ,CAAC,CAACoB,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACpB,UAAU,GAAGoB,QAAQ,CAACC,IAAI,IAAI,EAAE;QACrC,IAAI,CAACpB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACqB,oBAAoB,EAAE;QAC3B,IAAI,CAAC9B,EAAE,CAAC+B,aAAa,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACxB,UAAU,GAAG,EAAE;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACqB,oBAAoB,EAAE;QAC3B,IAAI,CAAC9B,EAAE,CAAC+B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEAG,kBAAkBA,CAACC,KAAa;IAC9BC,YAAY,CAAC,IAAI,CAACxB,aAAa,CAAC;IAChC,IAAI,CAACA,aAAa,GAAGyB,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC3B,cAAc,GAAG;QACpB,GAAG,IAAI,CAACA,cAAc;QACtB4B,QAAQ,EAAEH,KAAK,CAACI,IAAI;OACrB;MACD,IAAI,CAACvC,EAAE,CAAC+B,aAAa,EAAE;IACzB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAS,oBAAoBA,CAAA;IAClB,IAAI,CAACnC,uBAAuB,GAAG,CAAC,IAAI,CAACA,uBAAuB;EAC9D;EAEAoC,gBAAgBA,CAACC,OAAY;IAC3BT,OAAO,CAACU,GAAG,CAAC,mBAAmB,EAAED,OAAO,CAAC;IACzC,IAAI,CAACF,oBAAoB,EAAE;IAC3B,IAAI,CAAC9B,cAAc,GAAGgC,OAAO;IAC7B,IAAI,CAAC1C,EAAE,CAAC+B,aAAa,EAAE;EACzB;EAEA;EACAa,WAAWA,CAACC,YAAoB,EAAEC,YAAoB;IACpD,IAAI,CAACjC,mBAAmB,GAAGgC,YAAY;IACvC,IAAI,CAAC/B,mBAAmB,GAAGgC,YAAY;IAEvC,MAAMC,SAAS,GAAG,GAAGF,YAAY,IAAIC,YAAY,EAAE;IAEnD;IACA,IAAI,CAAC/B,mBAAmB,GAAG,CAACgC,SAAS,CAAC;IAEtC;IACA,IAAI,CAACrC,cAAc,GAAG;MACpB,GAAG,IAAI,CAACA,cAAc;MACtBmC,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BC,SAAS,EAAEA;KACZ;IAED,IAAI,CAAC/C,EAAE,CAAC+B,aAAa,EAAE;EACzB;EAEAiB,YAAYA,CAAA;IACV,IAAI,CAACnC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAE7B;IACA,MAAM;MAAE8B,YAAY;MAAEC,YAAY;MAAEC,SAAS;MAAE,GAAGE;IAAY,CAAE,GAAG,IAAI,CAACvC,cAAc;IACtF,IAAI,CAACA,cAAc,GAAGuC,YAAY;IAElC,IAAI,CAACjD,EAAE,CAAC+B,aAAa,EAAE;EACzB;EAEAmB,cAAcA,CAACL,YAAoB,EAAEC,YAAoB;IACvD,MAAMC,SAAS,GAAG,GAAGF,YAAY,IAAIC,YAAY,EAAE;IACnD,OAAO,IAAI,CAAC/B,mBAAmB,CAACoC,QAAQ,CAACJ,SAAS,CAAC;EACrD;EAEA;EACAK,eAAeA,CAAA;IACb,MAAMP,YAAY,GAAG,IAAI,CAAChC,mBAAmB;IAC7C,MAAMwC,IAAI,GAAG,IAAI,CAACvC,mBAAmB;IAErC,IAAI+B,YAAY,KAAK,kBAAkB,KAAKQ,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,UAAU,CAAC,EAAE;MACnL,OAAO,CAAC,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACrT,CAAC,MACI,IAAIR,YAAY,KAAK,kBAAkB,KAAKQ,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,gBAAgB,CAAC,EAAE;MAC9F,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IAC3S,CAAC,MACI,IAAIR,YAAY,KAAK,kBAAkB,KAAKQ,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAC,EAAE;MACxK,OAAO,CAAC,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IAChT,CAAC,MACI,IAAIR,YAAY,KAAK,kBAAkB,KAAKQ,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,CAAC,EAAE;MAC/F,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,iBAAiB,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACnR,CAAC,MACI,IAAIR,YAAY,KAAK,kBAAkB,KAAKQ,IAAI,KAAK,yBAAyB,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,iCAAiC,IAAIA,IAAI,KAAK,kBAAkB,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,kBAAkB,CAAC,EAAE;MACvV,OAAO,CAAC,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACpS,CAAC,MACI,IAAIR,YAAY,KAAK,iBAAiB,KAAKQ,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,SAAS,CAAC,EAAE;MAClK,OAAO,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IAC5U,CAAC,MACI,IAAIR,YAAY,KAAK,iBAAiB,KAAKQ,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,CAAC,EAAE;MACjI,OAAO,CAAC,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACzV,CAAC,MACI,IAAIR,YAAY,KAAK,iBAAiB,KAAKQ,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAC,EAAE;MACvK,OAAO,CAAC,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACrU,CAAC,MACI,IAAIR,YAAY,KAAK,SAAS,KAAKQ,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,gBAAgB,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,SAAS,CAAC,EAAE;MACjW,OAAO,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;IACjO,CAAC,MACI,IAAIR,YAAY,KAAK,SAAS,KAAKQ,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,YAAY,CAAC,EAAE;MAChN,OAAO,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,CAAC;IAC/N;IAEA,OAAO,EAAE;EACX;EAEA;EACAC,eAAeA,CAACC,SAAiB;IAC/B,OAAO,IAAI,CAACH,eAAe,EAAE,CAACD,QAAQ,CAACI,SAAS,CAAC;EACnD;EAEAhC,gBAAgBA,CAAA;IACd,IAAI,CAACxB,KAAK,CAACyD,WAAW,CAAC9B,SAAS,CAAE+B,MAAM,IAAI;MAC1C,IAAIA,MAAM,CAAC,SAAS,CAAC,KAAK,KAAK,EAAE;QAC/B,IAAI,CAACtD,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACuD,mBAAmB,EAAE;MAC5B,CAAC,MAAM,IAAID,MAAM,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE;QAC1C,IAAI,CAACrD,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACsD,mBAAmB,EAAE;MAC5B;IACF,CAAC,CAAC;EACJ;EAEA5B,oBAAoBA,CAAA;IAClB,IAAI,CAAC5B,aAAa,GAChB,IAAI,CAACM,UAAU,CAACmD,MAAM,KAAK,CAAC,IAC5B,CAAC,IAAI,CAACxD,eAAe,IACrB,CAAC,IAAI,CAACC,eAAe;EACzB;EAEAsD,mBAAmBA,CAAA;IACjBrB,UAAU,CAAC,MAAK;MACd,IAAI,CAAClC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAAC0B,oBAAoB,EAAE;IAC7B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA8B,aAAaA,CAAA;IACX,IAAI,CAACzD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC0B,oBAAoB,EAAE;IAC3B,IAAI,CAAC9B,EAAE,CAAC+B,aAAa,EAAE;EACzB;EAEA8B,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR9B,OAAO,CAACU,GAAG,CAAC,gBAAgB,EAAEoB,IAAI,CAACG,IAAI,CAAC;MACxC,IAAI,CAACC,gBAAgB,CAACJ,IAAI,CAAC;IAC7B;EACF;EAEAI,gBAAgBA,CAACJ,IAAU;IAAA,IAAAK,KAAA;IACzBnC,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAEoB,IAAI,CAACG,IAAI,CAAC;IACzC,IAAI,CAACpE,WAAW,CAACuE,gBAAgB,CAACN,IAAI,EAAE,IAAI,CAACzD,QAAQ,CAAC,CAACoB,SAAS,CAAC;MAC/DC,IAAI;QAAA,IAAA2C,IAAA,GAAAC,iBAAA,CAAE,WAAO3C,QAAQ,EAAI;UACvBK,OAAO,CAACU,GAAG,CAAC,oBAAoB,EAAEf,QAAQ,CAAC;UAC3CwC,KAAI,CAAClE,aAAa,GAAG,KAAK;UAC1BkE,KAAI,CAAC1D,cAAc,GAAG;YACpB,GAAG0D,KAAI,CAAC1D,cAAc;YACtB8D,gBAAgB,EAAEC,IAAI,CAACC,GAAG;WAC3B;UACDN,KAAI,CAAC5C,mBAAmB,EAAE;UAC1B4C,KAAI,CAACpE,EAAE,CAAC+B,aAAa,EAAE;UACvB;UACA,IAAIqC,KAAI,CAACnE,SAAS,IAAImE,KAAI,CAACnE,SAAS,CAAC0E,aAAa,EAAE;YAClDP,KAAI,CAACnE,SAAS,CAAC0E,aAAa,CAACxC,KAAK,GAAG,EAAE;UACzC;UACAiC,KAAI,CAACjE,eAAe,GAAG,IAAI;UAC3BiE,KAAI,CAACV,mBAAmB,EAAE;QAC5B,CAAC;QAAA,gBAfD/B,IAAIA,CAAAiD,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA,GAeH;MACD9C,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrCpC,IAAI,CAACmF,IAAI,CAAC,OAAO,EAAE,yCAAyC,EAAE,OAAO,CAAC;MACxE;KACD,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAClF,WAAW,CAACmF,qBAAqB,EAAE,CAACvD,SAAS,CAAC;MACjDC,IAAI,EAAGuD,IAAU,IAAI;QACnBvF,MAAM,CAACuF,IAAI,EAAE,qBAAqB,CAAC;MACrC,CAAC;MACDlD,KAAK,EAAGmD,GAAG,IAAKlD,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEmD,GAAG;KACrD,CAAC;EACJ;CACD;AA1OyBC,UAAA,EAAvB1F,SAAS,CAAC,WAAW,CAAC,C,4DAAyC;AADrDG,0BAA0B,GAAAuF,UAAA,EALtC3F,SAAS,CAAC;EACT4F,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,QAAQ,EAAE;CACX,CAAC,C,EACW1F,0BAA0B,CA2OtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}