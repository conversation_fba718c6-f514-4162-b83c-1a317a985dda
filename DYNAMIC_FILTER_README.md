# نظام الفلترة الديناميكية للجدول - Dynamic Table Filter System

## الوصف - Description

تم تطبيق نظام فلترة ديناميكي للجدول مشابه لنظام `getFieldsToShow()` الموجود في النماذج. هذا النظام يسمح بإظهار أعمدة مختلفة في الجدول بناءً على نوع المجمع (Compound Type) ونوع العقار (Property Type) المحددين.

## الميزات الجديدة - New Features

### 1. فلاتر ديناميكية منفصلة
- تم إضافة قسم منفصل في الفلتر للفلاتر الديناميكية
- فلتر نوع المجمع (Compound Type): خارج المجمع، داخل المجمع، قرية
- فلتر نوع العقار (Property Type): شقق، دوبلكس، استوديوهات، بنتهاوس، إلخ

### 2. إظهار الأعمدة الديناميكي
يتم إظهار أعمدة مختلفة بناءً على التركيبة المختارة:

#### خارج المجمع + شقق/دوبلكس/استوديوهات/بنتهاوس/أسطح/بدروم:
- رقم المبنى، رقم الوحدة، الطابق، مساحة الوحدة
- عدد الغرف، عدد الحمامات، اتجاه الوحدة، الإطلالة
- نوع التشطيب، حالة التسليم، الوضع القانوني
- نظام الدفع، السعر نقدي/تقسيط

#### خارج المجمع + فيلات/مباني كاملة:
- رقم المبنى، عدد الطوابق، مساحة المبنى، مساحة الأرض
- وصف الوحدة، تصميم الوحدة، اتجاه الوحدة، الإطلالة
- نوع التشطيب، الوضع القانوني، نظام الدفع

#### خارج المجمع + صيدليات/عيادات/وحدات إدارية/محلات تجارية:
- اسم المول، رقم المبنى، رقم الوحدة، الطابق
- مساحة الوحدة، الإطلالة، نوع التشطيب، حالة التجهيز
- حالة التسليم، النشاط، الوضع المالي، نظام الدفع

#### خارج المجمع + مخازن/مصانع:
- رقم المبنى، عدد الطوابق، مساحة الأرض، مساحة المبنى
- النشاط، نوع التشطيب، وصف الوحدة، الوضع القانوني
- نظام الدفع

#### خارج المجمع + أراضي (سكنية/إدارية/تجارية/طبية/مختلطة/صناعية):
- رقم الوحدة، مساحة الأرض، حالة التجهيز، وصف الوحدة
- موعد البناء، الإطلالة، الوضع القانوني، حالة التسليم
- الوضع المالي، نظام الدفع

#### داخل المجمع + شقق/دوبلكس/استوديوهات/بنتهاوس/فيلا داخلية:
- اسم المجمع، رقم المبنى، رقم الوحدة، الطابق
- مساحة الوحدة، عدد الغرف، عدد الحمامات، الإطلالة
- نوع التشطيب، حالة التسليم، الوضع المالي
- المطلوب على، نظام الدفع

#### داخل المجمع + فيلات منفصلة/توين هاوس/تاون هاوس:
- اسم المجمع، رقم المبنى، عدد الطوابق، مساحة المبنى
- مساحة الأرض، عدد الغرف، عدد الحمامات، الإطلالة
- نوع التشطيب، حالة التسليم، الوضع المالي
- المطلوب على، نظام الدفع

#### داخل المجمع + صيدليات/عيادات/وحدات إدارية/محلات:
- اسم المجمع، اسم المول، رقم المبنى، رقم الوحدة
- الطابق، مساحة الوحدة، الإطلالة، نوع التشطيب
- حالة التسليم، حالة التجهيز، الوضع المالي
- المطلوب على، نظام الدفع

#### قرية + وحدات سكنية (شقق/دوبلكس/استوديوهات/فيلات/شاليهات):
- رقم المبنى، رقم الوحدة، مساحة الوحدة، الطابق
- الإطلالة، عدد الغرف، عدد الحمامات، نوع التشطيب
- حالة الفرش، تكرار الإيجار، الإيجار اليومي/الشهري/السنوي

#### قرية + وحدات تجارية/صناعية (مخازن/مصانع/إدارية/طبية/تجارية):
- رقم المبنى، رقم الوحدة، مساحة الوحدة، الطابق
- الإطلالة، عدد الغرف، عدد الحمامات، نوع التشطيب
- حالة الفرش، تكرار الإيجار، النشاط
- الإيجار اليومي/الشهري

## كيفية الاستخدام - How to Use

1. **افتح صفحة البيانات والعقارات**
2. **اضغط على زر "Filter"**
3. **في قسم "Dynamic Table Filters":**
   - اختر نوع المجمع من القائمة المنسدلة
   - اختر نوع العقار من القائمة المنسدلة
4. **اضغط على "Apply Dynamic Filter"**
5. **سيتم إعادة تحميل الجدول لإظهار الأعمدة المناسبة فقط**

## الملفات المحدثة - Updated Files

### 1. unit-filter.component.ts
- إضافة فلاتر ديناميكية جديدة
- إضافة قوائم أنواع المجمعات والعقارات
- إضافة دالة `applyDynamicFilter()`

### 2. unit-filter.component.html
- إضافة قسم منفصل للفلاتر الديناميكية
- إضافة قوائم منسدلة لنوع المجمع ونوع العقار
- إضافة زر تطبيق الفلتر الديناميكي

### 3. dataandproperties.component.ts
- إضافة متغير `dynamicFilters`
- إضافة دالة `onDynamicFiltersApplied()`

### 4. dataandproperties.component.html
- تمرير الفلاتر الديناميكية للجدول
- ربط الأحداث الجديدة

### 5. propertiestable.component.ts
- إضافة `@Input() dynamicFilters`
- إضافة دالة `getFieldsToShow()` مع منطق الفلترة
- إضافة دالة `shouldShowField()`
- تحديث `ngOnChanges()` للتعامل مع الفلاتر الديناميكية

### 6. propertiestable.component.html
- تطبيق `*ngIf="shouldShowField()"` على جميع الأعمدة
- إضافة أعمدة جديدة للحقول الإضافية
- تطبيق الفلترة على رؤوس الجدول وجسم الجدول

## ملاحظات تقنية - Technical Notes

- النظام يستخدم نفس منطق `getFieldsToShow()` الموجود في النماذج
- الفلاتر الديناميكية لا تؤثر على استعلام API، فقط على عرض الأعمدة
- يتم الحفاظ على الفلاتر العادية منفصلة عن الفلاتر الديناميكية
- النظام متوافق مع الترتيب والبحث الموجود

## المتطلبات - Requirements

- Angular 15+
- FormsModule للـ ngModel
- Bootstrap للتنسيق
