<!-- Dynamic Filter Buttons -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card border-0 shadow-sm">
      <div class="card-body p-4">
        <h6 class="fw-bold text-dark mb-3">
          <i class="fa-solid fa-filter me-2 text-primary"></i>
          Dynamic Table Filters
        </h6>

        <div class="row g-3">
          <!-- Compound Type Filter -->
          <div class="col-md-4">
            <label class="form-label fw-semibold">Compound Type:</label>
            <select class="form-select form-select-sm" [(ngModel)]="dynamicFilter.compoundType">
              <option value="">All Compound Types</option>
              <option value="outside_compound">Outside Compound</option>
              <option value="inside_compound">Inside Compound</option>
              <option value="village">Village</option>
            </select>
          </div>

          <!-- Property Type Filter -->
          <div class="col-md-4">
            <label class="form-label fw-semibold">Property Type:</label>
            <select class="form-select form-select-sm" [(ngModel)]="dynamicFilter.propertyType">
              <option value="" >All Property Types</option>
              <option value="apartments">Apartments</option>
              <option value="duplexes">Duplexes</option>
              <option value="studios">Studios</option>
              <option value="penthouses">Penthouses</option>
              <option value="basement">Basement</option>
              <option value="roofs">Roofs</option>
              <option value="villas">Villas</option>
              <option value="standalone_villas">Standalone Villas</option>
              <option value="full_buildings">Full Buildings</option>
              <option value="twin_houses">Twin Houses</option>
              <option value="town_houses">Town Houses</option>
              <option value="chalets">Chalets</option>
              <option value="i_villa">I Villa</option>
              <option value="residential_buildings">Residential Buildings</option>
              <option value="commercial_administrative_buildings">Commercial Administrative Buildings</option>
              <option value="warehouses">Warehouses</option>
              <option value="factories">Factories</option>
              <option value="administrative_units">Administrative Units</option>
              <option value="medical_clinics">Medical Clinics</option>
              <option value="pharmacies">Pharmacies</option>
              <option value="commercial_stores">Commercial Stores</option>
              <option value="residential_villa_lands">Residential Villa Lands</option>
              <option value="residential_lands">Residential Lands</option>
              <option value="administrative_lands">Administrative Lands</option>
              <option value="commercial_administrative_lands">Commercial Administrative Lands</option>
              <option value="commercial_lands">Commercial Lands</option>
              <option value="medical_lands">Medical Lands</option>
              <option value="mixed_lands">Mixed Lands</option>
              <option value="warehouses_land">Warehouses Land</option>
              <option value="industrial_lands">Industrial Lands</option>
            </select>
          </div>

          <!-- Action Buttons -->
          <div class="col-md-4 d-flex align-items-end">
            <div class="d-flex gap-2 w-100">
              <button class="btn btn-success btn-sm flex-fill" (click)="applyDynamicFilter()">
                <i class="fa-solid fa-check me-1"></i>
                Apply Filter
              </button>
              
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="table-responsive mb-5">
  <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
    <thead>
      <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
        <th *ngIf="shouldShowField('type')" class="min-w-150px cursor-pointer ps-4"
          [ngClass]="{'rounded-start': shouldShowField('type')}" (click)="sortData('type')">
          Unit
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('type') }}</span>
        </th>
        <th *ngIf="shouldShowField('city')" class="min-w-150px cursor-pointer" (click)="sortData('city_id')">
          City
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('city_id') }}</span>
        </th>
        <th *ngIf="shouldShowField('area')" class="min-w-150px cursor-pointer" (click)="sortData('area_id')">
          Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('area_id') }}</span>
        </th>
        <th *ngIf="shouldShowField('location')" class="min-w-150px">
          Location on map
        </th>
        <th *ngIf="shouldShowField('compoundName')" class="min-w-150px cursor-pointer"
          (click)="sortData('compound_name')">
          Compound Name
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('compound_name') }}</span>
        </th>
        <th *ngIf="shouldShowField('mallName')" class="min-w-150px cursor-pointer" (click)="sortData('mall_name')">
          Mall Name
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('mall_name') }}</span>
        </th>
        <th *ngIf="shouldShowField('buildingNumber')" class="min-w-150px cursor-pointer"
          (click)="sortData('building_number')">
          Building Number
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_number') }}</span>
        </th>
        <th *ngIf="shouldShowField('unitNumber')" class="min-w-150px cursor-pointer" (click)="sortData('unit_number')">
          Unit Number
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_number') }}</span>
        </th>
        <th *ngIf="shouldShowField('numberOfFloors')" class="min-w-150px cursor-pointer"
          (click)="sortData('number_of_floors')">
          Number of Floors
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_floors') }}</span>
        </th>
        <th *ngIf="shouldShowField('buildingArea')" class="min-w-150px cursor-pointer"
          (click)="sortData('building_area')">
          Building Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_area') }}</span>
        </th>
        <th *ngIf="shouldShowField('groundArea')" class="min-w-150px cursor-pointer" (click)="sortData('ground_area')">
          Ground Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('ground_area') }}</span>
        </th>
        <th *ngIf="shouldShowField('unitArea')" class="min-w-150px cursor-pointer" (click)="sortData('unit_area')">
          Unit Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_area') }}</span>
        </th>
        <th *ngIf="shouldShowField('numberOfRooms')" class="min-w-150px cursor-pointer"
          (click)="sortData('number_of_rooms')">
          Number of Rooms
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_rooms') }}</span>
        </th>
        <th *ngIf="shouldShowField('numberOfBathrooms')" class="min-w-150px cursor-pointer"
          (click)="sortData('number_of_bathrooms')">
          Number of Bathrooms
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_bathrooms') }}</span>
        </th>
        <th *ngIf="shouldShowField('unitFacing')" class="min-w-150px cursor-pointer" (click)="sortData('unit_facing')">
          Unit Facing
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_facing') }}</span>
        </th>
        <th *ngIf="shouldShowField('view')" class="min-w-150px cursor-pointer" (click)="sortData('view')">
          View
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('view') }}</span>
        </th>
        <th *ngIf="shouldShowField('floor')" class="min-w-150px cursor-pointer" (click)="sortData('floor')">
          Floor
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('floor') }}</span>
        </th>
        <th *ngIf="shouldShowField('deliveryDate')" class="min-w-150px cursor-pointer"
          (click)="sortData('delivery_date')">
          Delivery date
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('delivery_date') }}</span>
        </th>
        <th *ngIf="shouldShowField('finishingType')" class="min-w-150px cursor-pointer"
          (click)="sortData('finishing_type')">
          Finishing state
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('finishing_type') }}</span>
        </th>
        <th *ngIf="shouldShowField('status')" class="min-w-150px cursor-pointer" (click)="sortData('status')">
          Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('status') }}</span>
        </th>
        <th *ngIf="shouldShowField('unitPlan')" class="min-w-150px">
          Unit plan
        </th>
        <!-- Additional fields for specific property types -->
        <th *ngIf="shouldShowField('unitDescription')" class="min-w-150px cursor-pointer"
          (click)="sortData('unit_description')">
          Unit Description
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_description') }}</span>
        </th>
        <th *ngIf="shouldShowField('unitDesign')" class="min-w-150px cursor-pointer" (click)="sortData('unit_design')">
          Unit Design
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_design') }}</span>
        </th>
        <th *ngIf="shouldShowField('deliveryStatus')" class="min-w-150px cursor-pointer"
          (click)="sortData('delivery_status')">
          Delivery Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('delivery_status') }}</span>
        </th>
        <th *ngIf="shouldShowField('legalStatus')" class="min-w-150px cursor-pointer"
          (click)="sortData('legal_status')">
          Legal Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('legal_status') }}</span>
        </th>
        <th *ngIf="shouldShowField('financialStatus')" class="min-w-150px cursor-pointer"
          (click)="sortData('financial_status')">
          Financial Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('financial_status') }}</span>
        </th>
        <th *ngIf="shouldShowField('fitOutCondition')" class="min-w-150px cursor-pointer"
          (click)="sortData('fit_out_condition')">
          Fit Out Condition
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('fit_out_condition') }}</span>
        </th>
        <th *ngIf="shouldShowField('activity')" class="min-w-150px cursor-pointer" (click)="sortData('activity')">
          Activity
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('activity') }}</span>
        </th>
        <th *ngIf="shouldShowField('buildingDeadline')" class="min-w-150px cursor-pointer"
          (click)="sortData('building_deadline')">
          Building Deadline
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_deadline') }}</span>
        </th>
        <th *ngIf="shouldShowField('requestedOver')" class="min-w-150px cursor-pointer"
          (click)="sortData('requested_over')">
          Requested Over
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('requested_over') }}</span>
        </th>
        <th *ngIf="shouldShowField('furnishingStatus')" class="min-w-150px cursor-pointer"
          (click)="sortData('furnishing_status')">
          Furnishing Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('furnishing_status') }}</span>
        </th>
        <th *ngIf="shouldShowField('rentRecurrence')" class="min-w-150px cursor-pointer"
          (click)="sortData('rent_recurrence')">
          Rent Recurrence
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('rent_recurrence') }}</span>
        </th>
        <th *ngIf="shouldShowField('dailyRent')" class="min-w-150px cursor-pointer" (click)="sortData('daily_rent')">
          Daily Rent
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('daily_rent') }}</span>
        </th>
        <th *ngIf="shouldShowField('monthlyRent')" class="min-w-150px cursor-pointer"
          (click)="sortData('monthly_rent')">
          Monthly Rent
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('monthly_rent') }}</span>
        </th>
        <th *ngIf="shouldShowField('annualRent')" class="min-w-150px cursor-pointer" (click)="sortData('annual_rent')">
          Annual Rent
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('annual_rent') }}</span>
        </th>
        <th *ngIf="shouldShowField('paymentSystem')" class="min-w-150px cursor-pointer"
          (click)="sortData('payment_system')">
          Payment System
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('payment_system') }}</span>
        </th>
        <th *ngIf="shouldShowField('pricePerMeterInCash')" class="min-w-150px cursor-pointer"
          (click)="sortData('price_per_meter_in_cash')">
          Price Per Meter (Cash)
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('price_per_meter_in_cash') }}</span>
        </th>
        <th *ngIf="shouldShowField('totalPriceInCash')" class="min-w-150px cursor-pointer"
          (click)="sortData('total_price_in_cash')">
          Total Price (Cash)
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('total_price_in_cash') }}</span>
        </th>
        <th *ngIf="shouldShowField('pricePerMeterInInstallment')" class="min-w-150px cursor-pointer"
          (click)="sortData('price_per_meter_in_installment')">
          Price Per Meter (Installment)
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('price_per_meter_in_installment') }}</span>
        </th>
        <th *ngIf="shouldShowField('totalPriceInInstallment')" class="min-w-150px cursor-pointer"
          (click)="sortData('total_price_in_installment')">
          Total Price (Installment)
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('total_price_in_installment') }}</span>
        </th>
        <th *ngIf="shouldShowField('otherAccessories')" class="min-w-200px cursor-pointer"
          (click)="sortData('other_accessories')">
          Other accessories
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('other_accessories') }}</span>
        </th>
        <th *ngIf="shouldShowField('actions')" class="min-w-50px text-end rounded-end pe-4">Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let property of rows">
        <td *ngIf="shouldShowField('type')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6 ps-4">
            {{ property.type }}
          </span>
        </td>
        <td *ngIf="shouldShowField('city')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.city?.name_en }}
          </span>
        </td>
        <td *ngIf="shouldShowField('area')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.area?.name_en }}
          </span>
        </td>
        <td *ngIf="shouldShowField('location')">
          <button class="btn btn-icon btn-sm btn-light-primary" data-bs-toggle="tooltip" title="View on map"
            (click)="showImageModal(property.location)">
            <i class="fa-solid fa-map-location-dot"></i>
          </button>
        </td>
        <td *ngIf="shouldShowField('compoundName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.compoundName }}
          </span>
        </td>
        <td *ngIf="shouldShowField('mallName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.mallName }}
          </span>
        </td>
        <td *ngIf="shouldShowField('buildingNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowField('unitNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowField('numberOfFloors')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfFloors }}
          </span>
        </td>
        <td *ngIf="shouldShowField('buildingArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingArea }}
          </span>
        </td>
        <td *ngIf="shouldShowField('groundArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.groundArea }}
          </span>
        </td>
        <td *ngIf="shouldShowField('unitArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitArea }}
          </span>
        </td>
        <td *ngIf="shouldShowField('numberOfRooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfRooms }}
          </span>
        </td>
        <td *ngIf="shouldShowField('numberOfBathrooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfBathrooms }}
          </span>
        </td>
        <td *ngIf="shouldShowField('unitFacing')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitFacing }}
          </span>
        </td>
        <td *ngIf="shouldShowField('view')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.view }}
          </span>
        </td>
        <td *ngIf="shouldShowField('floor')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.floor }}
          </span>
        </td>
        <td *ngIf="shouldShowField('deliveryDate')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.deliveryDate | date : "dd/MM/yyyy" }}
          </span>
        </td>
        <td *ngIf="shouldShowField('finishingType')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.finishingType }}
          </span>
        </td>
        <td *ngIf="shouldShowField('status')">
          <span class="badge badge-dark-blue">{{ property.status }}</span>
        </td>
        <td *ngIf="shouldShowField('unitPlan')">
          <button class="btn btn-sm btn-light-info" (click)="showUnitPlanModal(property.diagram)">
            <i class="fa-solid fa-file-image me-1"></i> View Plan
          </button>
        </td>
        <!-- Additional fields for specific property types -->
        <td *ngIf="shouldShowField('unitDescription')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitDescription }}
          </span>
        </td>
        <td *ngIf="shouldShowField('unitDesign')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitDesign }}
          </span>
        </td>
        <td *ngIf="shouldShowField('deliveryStatus')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.deliveryStatus }}
          </span>
        </td>
        <td *ngIf="shouldShowField('legalStatus')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.legalStatus }}
          </span>
        </td>
        <td *ngIf="shouldShowField('financialStatus')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.financialStatus }}
          </span>
        </td>
        <td *ngIf="shouldShowField('fitOutCondition')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.fitOutCondition }}
          </span>
        </td>
        <td *ngIf="shouldShowField('activity')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.activity }}
          </span>
        </td>
        <td *ngIf="shouldShowField('buildingDeadline')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingDeadline | date : "dd/MM/yyyy" }}
          </span>
        </td>
        <td *ngIf="shouldShowField('requestedOver')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.requestedOver }}
          </span>
        </td>
        <td *ngIf="shouldShowField('furnishingStatus')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.furnishingStatus }}
          </span>
        </td>
        <td *ngIf="shouldShowField('rentRecurrence')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.rentRecurrence }}
          </span>
        </td>
        <td *ngIf="shouldShowField('dailyRent')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.dailyRent | currency:'EGP':'symbol':'1.2-2' }}
          </span>
        </td>
        <td *ngIf="shouldShowField('monthlyRent')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.monthlyRent | currency:'EGP':'symbol':'1.2-2' }}
          </span>
        </td>
        <td *ngIf="shouldShowField('annualRent')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.annualRent | currency:'EGP':'symbol':'1.2-2' }}
          </span>
        </td>
        <td *ngIf="shouldShowField('paymentSystem')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.paymentSystem }}
          </span>
        </td>
        <td *ngIf="shouldShowField('pricePerMeterInCash')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.pricePerMeterInCash | currency:'EGP':'symbol':'1.2-2' }}
          </span>
        </td>
        <td *ngIf="shouldShowField('totalPriceInCash')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.totalPriceInCash | currency:'EGP':'symbol':'1.2-2' }}
          </span>
        </td>
        <td *ngIf="shouldShowField('pricePerMeterInInstallment')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.pricePerMeterInInstallment | currency:'EGP':'symbol':'1.2-2' }}
          </span>
        </td>
        <td *ngIf="shouldShowField('totalPriceInInstallment')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.totalPriceInInstallment | currency:'EGP':'symbol':'1.2-2' }}
          </span>
        </td>
        <td *ngIf="shouldShowField('otherAccessories')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.otherAccessories }}
          </span>
        </td>
        <td *ngIf="shouldShowField('actions')" class="text-end pe-4">
          <div class="dropdown">
            <button class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" type="button"
              data-bs-toggle="dropdown">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>
            <ul class="dropdown-menu">
              <li>
                <button class="dropdown-item" (click)="viewProperty(property)">
                  <i class="fa-solid fa-eye me-2"></i> View unit Details
                </button>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>

<app-view-apartment-model [selectedUnitPlanImage]="selectedUnitPlanImage"></app-view-apartment-model>