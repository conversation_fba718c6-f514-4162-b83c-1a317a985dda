<div class="table-responsive mb-5">
  <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
    <thead>
      <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
        <!-- Base columns - always shown -->
        <th class="min-w-150px cursor-pointer ps-4 rounded-start" (click)="sortData('type')"
          *ngIf="shouldShowColumn('type')">
          Unit Type
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('type') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('city_id')" *ngIf="shouldShowColumn('city')">
          City
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('city_id') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('area_id')" *ngIf="shouldShowColumn('area')">
          Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('area_id') }}</span>
        </th>
        <th class="min-w-150px" *ngIf="shouldShowColumn('location')">
          Location on map
        </th>

        <!-- Dynamic columns based on filter -->
        <th class="min-w-150px cursor-pointer" (click)="sortData('compound_name')"
          *ngIf="shouldShowColumn('compoundName')">
          Compound Name
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('compound_name') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('mall_name')" *ngIf="shouldShowColumn('mallName')">
          Mall Name
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('mall_name') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('building_number')"
          *ngIf="shouldShowColumn('buildingNumber')">
          Building Number
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_number') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('unit_number')" *ngIf="shouldShowColumn('unitNumber')">
          Unit Number
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_number') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('floor')" *ngIf="shouldShowColumn('floor')">
          Floor
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('floor') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('unit_area')" *ngIf="shouldShowColumn('unitArea')">
          Unit Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_area') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('ground_area')" *ngIf="shouldShowColumn('groundArea')">
          Ground Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('ground_area') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('building_area')"
          *ngIf="shouldShowColumn('buildingArea')">
          Building Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_area') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('number_of_rooms')"
          *ngIf="shouldShowColumn('numberOfRooms')">
          Rooms
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_rooms') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('number_of_bathrooms')"
          *ngIf="shouldShowColumn('numberOfBathrooms')">
          Bathrooms
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_bathrooms') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('number_of_floors')"
          *ngIf="shouldShowColumn('numberOfFloors')">
          Floors
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_floors') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('view')" *ngIf="shouldShowColumn('view')">
          View
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('view') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('finishing_type')"
          *ngIf="shouldShowColumn('finishingType')">
          Finishing Type
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('finishing_type') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('delivery_status')"
          *ngIf="shouldShowColumn('deliveryStatus')">
          Delivery Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('delivery_status') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('legal_status')"
          *ngIf="shouldShowColumn('legalStatus')">
          Legal Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('legal_status') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('financial_status')"
          *ngIf="shouldShowColumn('financialStatus')">
          Financial Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('financial_status') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('activity')" *ngIf="shouldShowColumn('activity')">
          Activity
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('activity') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('rent_recurrence')"
          *ngIf="shouldShowColumn('rentRecurrence')">
          Rent Recurrence
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('rent_recurrence') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('daily_rent')" *ngIf="shouldShowColumn('dailyRent')">
          Daily Rent
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('daily_rent') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('monthly_rent')"
          *ngIf="shouldShowColumn('monthlyRent')">
          Monthly Rent
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('monthly_rent') }}</span>
        </th>
        <th class="min-w-200px cursor-pointer" (click)="sortData('other_accessories')"
          *ngIf="shouldShowColumn('otherAccessories')">
          Other Accessories
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('other_accessories') }}</span>
        </th>

        <!-- Always show unit plan and actions -->
        <th class="min-w-150px" *ngIf="shouldShowColumn('unitPlan')">
          Unit Plan
        </th>
        <th class="min-w-50px text-end rounded-end pe-4" *ngIf="shouldShowColumn('actions')">Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let property of rows">
        <!-- Base columns - always shown -->
        <td *ngIf="shouldShowColumn('type')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6 ps-4">
            {{ property.type }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('city')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.city?.name_en }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('area')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.area?.name_en }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('location')">
          <button class="btn btn-icon btn-sm btn-light-primary" data-bs-toggle="tooltip" title="View on map"
            (click)="showImageModal(property.location)">
            <i class="fa-solid fa-map-location-dot"></i>
          </button>
        </td>

        <!-- Dynamic columns based on filter -->
        <td *ngIf="shouldShowColumn('compoundName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.compoundName }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('mallName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.mallName }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('buildingNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('floor')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.floor }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitArea }} m²
          </span>
        </td>
        <td *ngIf="shouldShowColumn('groundArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.groundArea }} m²
          </span>
        </td>
        <td *ngIf="shouldShowColumn('buildingArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingArea }} m²
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfRooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfRooms }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfBathrooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfBathrooms }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfFloors')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfFloors }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('view')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.view }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('finishingType')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.finishingType }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('deliveryStatus')">
          <span class="badge badge-light-success">{{ property.deliveryStatus }}</span>
        </td>
        <td *ngIf="shouldShowColumn('legalStatus')">
          <span class="badge badge-light-info">{{ property.legalStatus }}</span>
        </td>
        <td *ngIf="shouldShowColumn('financialStatus')">
          <span class="badge badge-light-warning">{{ property.financialStatus }}</span>
        </td>
        <td *ngIf="shouldShowColumn('activity')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.activity }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('rentRecurrence')">
          <span class="badge badge-light-primary">{{ property.rentRecurrence }}</span>
        </td>
        <td *ngIf="shouldShowColumn('dailyRent')">
          <span class="text-success fw-bold d-block mb-1 fs-6">
            {{ property.dailyRent | currency:'EGP':'symbol':'1.0-0' }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('monthlyRent')">
          <span class="text-success fw-bold d-block mb-1 fs-6">
            {{ property.monthlyRent | currency:'EGP':'symbol':'1.0-0' }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('otherAccessories')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.otherAccessories }}
          </span>
        </td>

        <!-- Always show unit plan and actions -->
        <td *ngIf="shouldShowColumn('unitPlan')">
          <button class="btn btn-sm btn-light-info" (click)="showUnitPlanModal(property.diagram)">
            <i class="fa-solid fa-file-image me-1"></i> View Plan
          </button>
        </td>
        <td class="text-end pe-4" *ngIf="shouldShowColumn('actions')">
          <div class="dropdown">
            <button class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" type="button"
              data-bs-toggle="dropdown">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>
            <ul class="dropdown-menu">
              <li>
                <button class="dropdown-item" (click)="viewProperty(property)">
                  <i class="fa-solid fa-eye me-2"></i> View unit Details
                </button>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>

<app-view-apartment-model [selectedUnitPlanImage]="selectedUnitPlanImage"></app-view-apartment-model>