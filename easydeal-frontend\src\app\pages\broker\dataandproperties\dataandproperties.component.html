<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <!-- Header Section -->
    <div class="row mb-6">
      <div class="col-12">
        <div
          class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center justify-content-between gap-3 mt-2">

          <!-- Left: Title Section -->
          <div class="flex-shrink-0">
            <h1 class="text-dark-blue fs-2 fs-lg-1 fw-bolder mb-1">
              Data and Properties
            </h1>
            <p class="text-muted fs-6 mb-0">
              View and manage your property data
            </p>
          </div>

          <!-- Center: Search -->
          <div class="flex-grow-1 mx-lg-4" style="max-width: 400px;">
            <div class="position-relative">
              <app-keenicon name="magnifier" class="fs-3 text-gray-500 position-absolute top-50 translate-middle-y ms-4"
                type="outline">
              </app-keenicon>
              <input type="text" name="searchText"
                class="form-control form-control-lg ps-12 bg-light border-0 rounded-3" [(ngModel)]="searchText"
                (ngModelChange)="onSearchTextChange($event)" placeholder="Search by unit type..." />
            </div>
          </div>

          <!-- Right: Action Buttons -->
          <div class="flex-shrink-0">
            <div class="d-flex flex-column flex-sm-row gap-2">

              <!-- Filter Button -->
              <div class="position-relative">
                <button type="button" class="btn btn-light-primary btn-sm px-3 py-2" (click)="toggleFilterDropdown()">
                  <i class="fa-solid fa-filter me-2"></i>
                  <span class="d-none d-md-inline">Filter</span>
                </button>

                <!-- Filter Dropdown -->
                <div *ngIf="isFilterDropdownVisible" class="dropdown-menu show p-3 shadow-lg border-0 rounded-3" style="
                    position: absolute;
                    top: 100%;
                    right: 0;
                    z-index: 1000;
                    min-width: 320px;
                    max-width: 90vw;
                  ">
                  <app-unit-filter (filtersApplied)="onFiltersApplied($event)"></app-unit-filter>
                </div>
              </div>

              <!-- Upload Units -->
              <input type="file" #fileInput (change)="onFileSelected($event)" accept=".xlsx,.xls" hidden />
              <button type="button" class="btn btn-light-success btn-sm px-3 py-2" (click)="fileInput.click()">
                <i class="fa-solid fa-upload me-2"></i>
                <span class="d-none d-md-inline">Upload</span>
              </button>

              <!-- Download Template -->
              <button type="button" class="btn btn-light-info btn-sm px-3 py-2" (click)="downloadTemplate()">
                <i class="fa-solid fa-download me-2"></i>
                <span class="d-none d-md-inline">Template</span>
              </button>

              <!-- Add New Property -->
              <button type="button" class="btn btn-primary btn-sm px-3 py-2 fw-bold"
                [routerLink]="['/broker/add-property']">
                <i class="fa-solid fa-plus me-2"></i>
                <span class="d-none d-lg-inline">Add Unit</span>
                <span class="d-lg-none">Add</span>
              </button>

            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center py-10">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Properties Table -->
    <app-propertiestable *ngIf="
        !isLoading && !showEmptyCard && !showSuccessCard && !showPublishCard
      " [appliedFilters]="appliedFilters" [dynamicFilters]="dynamicFilters">
    </app-propertiestable>

    <!-- Empty Properties Card -->
    <app-empty-properties-card *ngIf="!isLoading && showEmptyCard" [userRole]="user.role"
      [onFileUpload]="handleFileUpload.bind(this)" [onDownloadTemplate]="downloadTemplate.bind(this)">
    </app-empty-properties-card>

    <!-- Publish Property Card -->
    <app-publish-property-card *ngIf="!isLoading && showPublishCard" (backToTable)="onBackToTable()">
    </app-publish-property-card>

    <!-- Success Adding Property Card -->
    <app-success-adding-property-card *ngIf="!isLoading && showSuccessCard" (backToTable)="onBackToTable()">
    </app-success-adding-property-card>
  </div>
</div>