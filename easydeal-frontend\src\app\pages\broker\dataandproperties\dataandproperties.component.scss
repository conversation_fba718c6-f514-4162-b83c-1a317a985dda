// Filter buttons styling
.filter-buttons {
  .btn {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    font-weight: 500;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
    }

    &.btn-light-primary {
      background-color: rgba(102, 126, 234, 0.1);
      border-color: rgba(102, 126, 234, 0.2);
      color: #667eea;

      &:hover {
        background-color: rgba(102, 126, 234, 0.2);
        border-color: rgba(102, 126, 234, 0.3);
      }
    }

    &.btn-success {
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      border: none;
      color: white;
    }

    &.btn-light-success {
      background-color: rgba(40, 167, 69, 0.1);
      border-color: rgba(40, 167, 69, 0.2);
      color: #28a745;
    }

    &.btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
      border: none;
      color: white;
    }

    &.btn-light-info {
      background-color: rgba(23, 162, 184, 0.1);
      border-color: rgba(23, 162, 184, 0.2);
      color: #17a2b8;
    }

    &.btn-warning {
      background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
      border: none;
      color: #212529;
    }

    &.btn-light-warning {
      background-color: rgba(255, 193, 7, 0.1);
      border-color: rgba(255, 193, 7, 0.2);
      color: #ffc107;
    }

    &.btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
      border: none;
      color: white;
    }

    &.btn-light-secondary {
      background-color: rgba(108, 117, 125, 0.1);
      border-color: rgba(108, 117, 125, 0.2);
      color: #6c757d;
    }

    &.btn-light-danger {
      background-color: rgba(220, 53, 69, 0.1);
      border-color: rgba(220, 53, 69, 0.2);
      color: #dc3545;

      &:hover {
        background-color: rgba(220, 53, 69, 0.2);
        border-color: rgba(220, 53, 69, 0.3);
      }
    }
  }
}

// Card styling
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e3ea;

  .card-body {
    padding: 1.5rem;
  }
}

// Text colors
.text-dark-blue {
  color: #2d3748 !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .filter-buttons {
    .btn {
      font-size: 0.875rem;
      padding: 0.5rem 0.75rem;
    }
  }
}
