{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { saveAs } from 'file-saver';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/unit.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../_metronic/shared/keenicon/keenicon.component\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../shared/broker-title/broker-title.component\";\nimport * as i7 from \"./components/propertiestable/propertiestable.component\";\nimport * as i8 from \"./components/empty-properties-card/empty-properties-card.component\";\nimport * as i9 from \"./components/success-adding-property-card/success-adding-property-card.component\";\nimport * as i10 from \"./components/publish-property-card/publish-property-card.component\";\nimport * as i11 from \"./components/unit-filter/unit-filter.component\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = () => [\"/broker/add-property\"];\nfunction DataandpropertiesComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"app-unit-filter\", 34);\n    i0.ɵɵlistener(\"filtersApplied\", function DataandpropertiesComponent_div_23_Template_app_unit_filter_filtersApplied_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFiltersApplied($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataandpropertiesComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"span\", 37);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DataandpropertiesComponent_div_41_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵtext(2, \" Clear All Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DataandpropertiesComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"div\", 40)(3, \"div\", 41)(4, \"h5\", 42);\n    i0.ɵɵelement(5, \"i\", 16);\n    i0.ɵɵtext(6, \" Property Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, DataandpropertiesComponent_div_41_button_7_Template, 3, 0, \"button\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 44)(9, \"div\", 5)(10, \"h6\", 45);\n    i0.ɵɵtext(11, \"Outside Compound\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 46)(13, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"apartments\"));\n    });\n    i0.ɵɵelement(14, \"i\", 48);\n    i0.ɵɵtext(15, \" Apartments \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 46)(17, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"duplexes\"));\n    });\n    i0.ɵɵelement(18, \"i\", 49);\n    i0.ɵɵtext(19, \" Duplexes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 46)(21, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"studios\"));\n    });\n    i0.ɵɵelement(22, \"i\", 50);\n    i0.ɵɵtext(23, \" Studios \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 46)(25, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"penthouses\"));\n    });\n    i0.ɵɵelement(26, \"i\", 51);\n    i0.ɵɵtext(27, \" Penthouses \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 46)(29, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"villas\"));\n    });\n    i0.ɵɵelement(30, \"i\", 52);\n    i0.ɵɵtext(31, \" Villas \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 46)(33, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"commercial_stores\"));\n    });\n    i0.ɵɵelement(34, \"i\", 53);\n    i0.ɵɵtext(35, \" Commercial Stores \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 46)(37, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"warehouses\"));\n    });\n    i0.ɵɵelement(38, \"i\", 54);\n    i0.ɵɵtext(39, \" Warehouses \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 46)(41, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"factories\"));\n    });\n    i0.ɵɵelement(42, \"i\", 55);\n    i0.ɵɵtext(43, \" Factories \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 46)(45, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"outside_compound\", \"residential_villa_lands\"));\n    });\n    i0.ɵɵelement(46, \"i\", 56);\n    i0.ɵɵtext(47, \" Villa Lands \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 57)(49, \"h6\", 45);\n    i0.ɵɵtext(50, \"Inside Compound\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 46)(52, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"inside_compound\", \"apartments\"));\n    });\n    i0.ɵɵelement(53, \"i\", 48);\n    i0.ɵɵtext(54, \" Compound Apartments \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 46)(56, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"inside_compound\", \"standalone_villas\"));\n    });\n    i0.ɵɵelement(57, \"i\", 52);\n    i0.ɵɵtext(58, \" Standalone Villas \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 57)(60, \"h6\", 45);\n    i0.ɵɵtext(61, \"Village (Rent Only)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 46)(63, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_63_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"village\", \"apartments\"));\n    });\n    i0.ɵɵelement(64, \"i\", 58);\n    i0.ɵɵtext(65, \" Village Apartments \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 46)(67, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_div_41_Template_button_click_67_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilter(\"village\", \"chalets\"));\n    });\n    i0.ɵɵelement(68, \"i\", 59);\n    i0.ɵɵtext(69, \" Chalets \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.activeFilterButtons.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"apartments\") ? \"btn-primary\" : \"btn-light-primary\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"duplexes\") ? \"btn-primary\" : \"btn-light-primary\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"studios\") ? \"btn-primary\" : \"btn-light-primary\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"penthouses\") ? \"btn-primary\" : \"btn-light-primary\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"villas\") ? \"btn-primary\" : \"btn-light-primary\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"commercial_stores\") ? \"btn-success\" : \"btn-light-success\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"warehouses\") ? \"btn-success\" : \"btn-light-success\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"factories\") ? \"btn-success\" : \"btn-light-success\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"outside_compound\", \"residential_villa_lands\") ? \"btn-warning\" : \"btn-light-warning\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"inside_compound\", \"apartments\") ? \"btn-info\" : \"btn-light-info\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"inside_compound\", \"standalone_villas\") ? \"btn-info\" : \"btn-light-info\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"village\", \"apartments\") ? \"btn-secondary\" : \"btn-light-secondary\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.isFilterActive(\"village\", \"chalets\") ? \"btn-secondary\" : \"btn-light-secondary\");\n  }\n}\nfunction DataandpropertiesComponent_app_propertiestable_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-propertiestable\", 62);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"appliedFilters\", ctx_r2.appliedFilters);\n  }\n}\nfunction DataandpropertiesComponent_app_empty_properties_card_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-empty-properties-card\", 63);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"userRole\", ctx_r2.user.role)(\"onFileUpload\", ctx_r2.handleFileUpload.bind(ctx_r2))(\"onDownloadTemplate\", ctx_r2.downloadTemplate.bind(ctx_r2));\n  }\n}\nfunction DataandpropertiesComponent_app_publish_property_card_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-publish-property-card\", 64);\n    i0.ɵɵlistener(\"backToTable\", function DataandpropertiesComponent_app_publish_property_card_44_Template_app_publish_property_card_backToTable_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onBackToTable());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DataandpropertiesComponent_app_success_adding_property_card_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-success-adding-property-card\", 64);\n    i0.ɵɵlistener(\"backToTable\", function DataandpropertiesComponent_app_success_adding_property_card_45_Template_app_success_adding_property_card_backToTable_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onBackToTable());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class DataandpropertiesComponent {\n  unitService;\n  route;\n  cd;\n  fileInput;\n  showEmptyCard = false;\n  showSuccessCard = false;\n  showPublishCard = false;\n  isFilterDropdownVisible = false;\n  brokerId;\n  user;\n  properties = [];\n  isLoading = false;\n  appliedFilters = {};\n  searchText = '';\n  searchTimeout;\n  // Filter properties\n  currentCompoundType = '';\n  currentPropertyType = '';\n  activeFilterButtons = [];\n  constructor(unitService, route, cd) {\n    this.unitService = unitService;\n    this.route = route;\n    this.cd = cd;\n  }\n  ngOnInit() {\n    const userJson = localStorage.getItem('currentUser');\n    this.user = userJson ? JSON.parse(userJson) : null;\n    this.brokerId = this.user?.brokerId;\n    this.checkRouteParams();\n    this.loadPropertiesCount();\n  }\n  loadPropertiesCount() {\n    this.isLoading = true;\n    this.unitService.getByBrokerId(this.brokerId).subscribe({\n      next: response => {\n        this.properties = response.data || [];\n        this.isLoading = false;\n        this.updateCardVisibility();\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading properties:', error);\n        this.properties = [];\n        this.isLoading = false;\n        this.updateCardVisibility();\n        this.cd.detectChanges();\n      }\n    });\n  }\n  onSearchTextChange(value) {\n    clearTimeout(this.searchTimeout);\n    this.searchTimeout = setTimeout(() => {\n      this.appliedFilters = {\n        ...this.appliedFilters,\n        unitType: value.trim()\n      };\n      this.cd.detectChanges();\n    }, 300);\n  }\n  toggleFilterDropdown() {\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\n  }\n  onFiltersApplied(filters) {\n    console.log('Received filters:', filters);\n    this.toggleFilterDropdown();\n    this.appliedFilters = filters;\n    this.cd.detectChanges();\n  }\n  // Filter button methods\n  applyFilter(compoundType, propertyType) {\n    this.currentCompoundType = compoundType;\n    this.currentPropertyType = propertyType;\n    const filterKey = `${compoundType}_${propertyType}`;\n    // Update active filter buttons\n    this.activeFilterButtons = [filterKey];\n    // Apply filters to table\n    this.appliedFilters = {\n      ...this.appliedFilters,\n      compoundType: compoundType,\n      propertyType: propertyType,\n      filterKey: filterKey\n    };\n    this.cd.detectChanges();\n  }\n  clearFilters() {\n    this.currentCompoundType = '';\n    this.currentPropertyType = '';\n    this.activeFilterButtons = [];\n    // Remove compound and property type filters but keep other filters\n    const {\n      compoundType,\n      propertyType,\n      filterKey,\n      ...otherFilters\n    } = this.appliedFilters;\n    this.appliedFilters = otherFilters;\n    this.cd.detectChanges();\n  }\n  isFilterActive(compoundType, propertyType) {\n    const filterKey = `${compoundType}_${propertyType}`;\n    return this.activeFilterButtons.includes(filterKey);\n  }\n  // Get fields to show based on compound type and property type\n  getFieldsToShow() {\n    const compoundType = this.currentCompoundType;\n    const type = this.currentPropertyType;\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\n      return ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\n      return ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      return ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\n      return ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\n      return ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\n      return ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\n      return ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      return ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'];\n    } else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];\n    }\n    return [];\n  }\n  // Check if a specific field should be shown\n  shouldShowField(fieldName) {\n    return this.getFieldsToShow().includes(fieldName);\n  }\n  checkRouteParams() {\n    this.route.queryParams.subscribe(params => {\n      if (params['success'] === 'add') {\n        this.showSuccessCard = true;\n        this.hideCardsAfterDelay();\n      } else if (params['success'] === 'publish') {\n        this.showPublishCard = true;\n        this.hideCardsAfterDelay();\n      }\n    });\n  }\n  updateCardVisibility() {\n    this.showEmptyCard = this.properties.length === 0 && !this.showSuccessCard && !this.showPublishCard;\n  }\n  hideCardsAfterDelay() {\n    setTimeout(() => {\n      this.showSuccessCard = false;\n      this.showPublishCard = false;\n      this.updateCardVisibility();\n    }, 5000);\n  }\n  onBackToTable() {\n    this.showSuccessCard = false;\n    this.showPublishCard = false;\n    this.updateCardVisibility();\n    this.cd.detectChanges();\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      console.log('File selected:', file.name);\n      this.handleFileUpload(file);\n    }\n  }\n  handleFileUpload(file) {\n    var _this = this;\n    console.log('Uploading file:', file.name);\n    this.unitService.uploadExcelUnits(file, this.brokerId).subscribe({\n      next: function () {\n        var _ref = _asyncToGenerator(function* (response) {\n          console.log('Upload successful:', response);\n          _this.showEmptyCard = false;\n          _this.appliedFilters = {\n            ..._this.appliedFilters,\n            refreshTimestamp: Date.now()\n          };\n          _this.loadPropertiesCount();\n          _this.cd.detectChanges();\n          // Reset file input\n          if (_this.fileInput && _this.fileInput.nativeElement) {\n            _this.fileInput.nativeElement.value = '';\n          }\n          _this.showSuccessCard = true;\n          _this.hideCardsAfterDelay();\n        });\n        return function next(_x) {\n          return _ref.apply(this, arguments);\n        };\n      }(),\n      error: error => {\n        console.error('Upload error:', error);\n        Swal.fire('Error', 'Error uploading file. Please try again.', 'error');\n      }\n    });\n  }\n  downloadTemplate() {\n    this.unitService.downloadExcelTemplate().subscribe({\n      next: blob => {\n        saveAs(blob, 'units-template.xlsx');\n      },\n      error: err => console.error('Download error:', err)\n    });\n  }\n  static ɵfac = function DataandpropertiesComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DataandpropertiesComponent)(i0.ɵɵdirectiveInject(i1.UnitService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DataandpropertiesComponent,\n    selectors: [[\"app-dataandproperties\"]],\n    viewQuery: function DataandpropertiesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n      }\n    },\n    decls: 46,\n    vars: 10,\n    consts: [[\"fileInput\", \"\"], [1, \"mb-5\", \"mt-0\"], [1, \"card\", \"mb-5\", \"mb-xl-10\"], [1, \"card-body\", \"pt-3\", \"pb-0\"], [1, \"row\", \"mb-6\"], [1, \"col-12\"], [1, \"d-flex\", \"flex-column\", \"flex-lg-row\", \"align-items-start\", \"align-items-lg-center\", \"justify-content-between\", \"gap-3\", \"mt-2\"], [1, \"flex-shrink-0\"], [1, \"text-dark-blue\", \"fs-2\", \"fs-lg-1\", \"fw-bolder\", \"mb-1\"], [1, \"text-muted\", \"fs-6\", \"mb-0\"], [1, \"flex-grow-1\", \"mx-lg-4\", 2, \"max-width\", \"400px\"], [1, \"position-relative\"], [\"name\", \"magnifier\", \"type\", \"outline\", 1, \"fs-3\", \"text-gray-500\", \"position-absolute\", \"top-50\", \"translate-middle-y\", \"ms-4\"], [\"type\", \"text\", \"name\", \"searchText\", \"placeholder\", \"Search by unit type...\", 1, \"form-control\", \"form-control-lg\", \"ps-12\", \"bg-light\", \"border-0\", \"rounded-3\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"flex-column\", \"flex-sm-row\", \"gap-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-light-primary\", \"btn-sm\", \"px-3\", \"py-2\", 3, \"click\"], [1, \"fa-solid\", \"fa-filter\", \"me-2\"], [1, \"d-none\", \"d-md-inline\"], [\"class\", \"dropdown-menu show p-3 shadow-lg border-0 rounded-3\", \"style\", \"\\n                    position: absolute;\\n                    top: 100%;\\n                    right: 0;\\n                    z-index: 1000;\\n                    min-width: 280px;\\n                    max-width: 90vw;\\n                  \", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", \"hidden\", \"\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-light-success\", \"btn-sm\", \"px-3\", \"py-2\", 3, \"click\"], [1, \"fa-solid\", \"fa-upload\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-light-info\", \"btn-sm\", \"px-3\", \"py-2\", 3, \"click\"], [1, \"fa-solid\", \"fa-download\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"px-3\", \"py-2\", \"fw-bold\", 3, \"routerLink\"], [1, \"fa-solid\", \"fa-plus\", \"me-2\"], [1, \"d-none\", \"d-lg-inline\"], [1, \"d-lg-none\"], [\"class\", \"d-flex justify-content-center align-items-center py-10\", 4, \"ngIf\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [3, \"appliedFilters\", 4, \"ngIf\"], [3, \"userRole\", \"onFileUpload\", \"onDownloadTemplate\", 4, \"ngIf\"], [3, \"backToTable\", 4, \"ngIf\"], [1, \"dropdown-menu\", \"show\", \"p-3\", \"shadow-lg\", \"border-0\", \"rounded-3\", 2, \"position\", \"absolute\", \"top\", \"100%\", \"right\", \"0\", \"z-index\", \"1000\", \"min-width\", \"280px\", \"max-width\", \"90vw\"], [3, \"filtersApplied\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-10\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mb-4\"], [1, \"card\", \"border-0\", \"shadow-sm\"], [1, \"card-body\", \"p-4\"], [1, \"d-flex\", \"flex-wrap\", \"align-items-center\", \"justify-content-between\", \"mb-3\"], [1, \"text-dark-blue\", \"fw-bold\", \"mb-0\"], [\"class\", \"btn btn-sm btn-light-danger\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"g-2\"], [1, \"text-muted\", \"fw-semibold\", \"mb-2\"], [1, \"col-lg-3\", \"col-md-4\", \"col-sm-6\"], [1, \"btn\", \"btn-sm\", \"w-100\", \"fw-medium\", 3, \"click\"], [1, \"fa-solid\", \"fa-building\", \"me-1\"], [1, \"fa-solid\", \"fa-home\", \"me-1\"], [1, \"fa-solid\", \"fa-door-open\", \"me-1\"], [1, \"fa-solid\", \"fa-crown\", \"me-1\"], [1, \"fa-solid\", \"fa-house\", \"me-1\"], [1, \"fa-solid\", \"fa-store\", \"me-1\"], [1, \"fa-solid\", \"fa-warehouse\", \"me-1\"], [1, \"fa-solid\", \"fa-industry\", \"me-1\"], [1, \"fa-solid\", \"fa-map\", \"me-1\"], [1, \"col-12\", \"mt-3\"], [1, \"fa-solid\", \"fa-calendar-days\", \"me-1\"], [1, \"fa-solid\", \"fa-umbrella-beach\", \"me-1\"], [1, \"btn\", \"btn-sm\", \"btn-light-danger\", 3, \"click\"], [1, \"fa-solid\", \"fa-times\", \"me-1\"], [3, \"appliedFilters\"], [3, \"userRole\", \"onFileUpload\", \"onDownloadTemplate\"], [3, \"backToTable\"]],\n    template: function DataandpropertiesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵelement(1, \"app-broker-title\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h1\", 8);\n        i0.ɵɵtext(9, \" Data and Properties \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"p\", 9);\n        i0.ɵɵtext(11, \" View and manage your property data \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11);\n        i0.ɵɵelement(14, \"app-keenicon\", 12);\n        i0.ɵɵelementStart(15, \"input\", 13);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function DataandpropertiesComponent_Template_input_ngModelChange_15_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.searchText, $event) || (ctx.searchText = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵlistener(\"ngModelChange\", function DataandpropertiesComponent_Template_input_ngModelChange_15_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSearchTextChange($event));\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(16, \"div\", 7)(17, \"div\", 14)(18, \"div\", 11)(19, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_Template_button_click_19_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleFilterDropdown());\n        });\n        i0.ɵɵelement(20, \"i\", 16);\n        i0.ɵɵelementStart(21, \"span\", 17);\n        i0.ɵɵtext(22, \"Filter\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(23, DataandpropertiesComponent_div_23_Template, 2, 0, \"div\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"input\", 19, 0);\n        i0.ɵɵlistener(\"change\", function DataandpropertiesComponent_Template_input_change_24_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFileSelected($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_Template_button_click_26_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const fileInput_r4 = i0.ɵɵreference(25);\n          return i0.ɵɵresetView(fileInput_r4.click());\n        });\n        i0.ɵɵelement(27, \"i\", 21);\n        i0.ɵɵelementStart(28, \"span\", 17);\n        i0.ɵɵtext(29, \"Upload\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"button\", 22);\n        i0.ɵɵlistener(\"click\", function DataandpropertiesComponent_Template_button_click_30_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.downloadTemplate());\n        });\n        i0.ɵɵelement(31, \"i\", 23);\n        i0.ɵɵelementStart(32, \"span\", 17);\n        i0.ɵɵtext(33, \"Template\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"button\", 24);\n        i0.ɵɵelement(35, \"i\", 25);\n        i0.ɵɵelementStart(36, \"span\", 26);\n        i0.ɵɵtext(37, \"Add Unit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"span\", 27);\n        i0.ɵɵtext(39, \"Add\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵtemplate(40, DataandpropertiesComponent_div_40_Template, 4, 0, \"div\", 28)(41, DataandpropertiesComponent_div_41_Template, 70, 27, \"div\", 29)(42, DataandpropertiesComponent_app_propertiestable_42_Template, 1, 1, \"app-propertiestable\", 30)(43, DataandpropertiesComponent_app_empty_properties_card_43_Template, 1, 3, \"app-empty-properties-card\", 31)(44, DataandpropertiesComponent_app_publish_property_card_44_Template, 1, 0, \"app-publish-property-card\", 32)(45, DataandpropertiesComponent_app_success_adding_property_card_45_Template, 1, 0, \"app-success-adding-property-card\", 32);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(15);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchText);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.isFilterDropdownVisible);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(9, _c1));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.showEmptyCard && !ctx.showSuccessCard && !ctx.showPublishCard);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.showEmptyCard && !ctx.showSuccessCard && !ctx.showPublishCard);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.showEmptyCard);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.showPublishCard);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.showSuccessCard);\n      }\n    },\n    dependencies: [i2.RouterLink, i3.KeeniconComponent, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.BrokerTitleComponent, i7.PropertiestableComponent, i8.EmptyPropertiesCardComponent, i9.SuccessAddingPropertyCardComponent, i10.PublishPropertyCardComponent, i11.UnitFilterComponent],\n    styles: [\".filter-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  color: white;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-primary[_ngcontent-%COMP%] {\\n  background-color: rgba(102, 126, 234, 0.1);\\n  border-color: rgba(102, 126, 234, 0.2);\\n  color: #667eea;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-primary[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(102, 126, 234, 0.2);\\n  border-color: rgba(102, 126, 234, 0.3);\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  color: white;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-success[_ngcontent-%COMP%] {\\n  background-color: rgba(40, 167, 69, 0.1);\\n  border-color: rgba(40, 167, 69, 0.2);\\n  color: #28a745;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);\\n  border: none;\\n  color: white;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-info[_ngcontent-%COMP%] {\\n  background-color: rgba(23, 162, 184, 0.1);\\n  border-color: rgba(23, 162, 184, 0.2);\\n  color: #17a2b8;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\\n  border: none;\\n  color: #212529;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-warning[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 193, 7, 0.1);\\n  border-color: rgba(255, 193, 7, 0.2);\\n  color: #ffc107;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\\n  border: none;\\n  color: white;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-secondary[_ngcontent-%COMP%] {\\n  background-color: rgba(108, 117, 125, 0.1);\\n  border-color: rgba(108, 117, 125, 0.2);\\n  color: #6c757d;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-danger[_ngcontent-%COMP%] {\\n  background-color: rgba(220, 53, 69, 0.1);\\n  border-color: rgba(220, 53, 69, 0.2);\\n  color: #dc3545;\\n}\\n.filter-buttons[_ngcontent-%COMP%]   .btn.btn-light-danger[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(220, 53, 69, 0.2);\\n  border-color: rgba(220, 53, 69, 0.3);\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e1e3ea;\\n}\\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n\\n.text-dark-blue[_ngcontent-%COMP%] {\\n  color: #2d3748 !important;\\n}\\n\\n@media (max-width: 768px) {\\n  .filter-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.5rem 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["saveAs", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "DataandpropertiesComponent_div_23_Template_app_unit_filter_filtersApplied_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onFiltersApplied", "ɵɵelementEnd", "ɵɵtext", "DataandpropertiesComponent_div_41_button_7_Template_button_click_0_listener", "_r6", "clearFilters", "ɵɵelement", "ɵɵtemplate", "DataandpropertiesComponent_div_41_button_7_Template", "DataandpropertiesComponent_div_41_Template_button_click_13_listener", "_r5", "applyFilter", "DataandpropertiesComponent_div_41_Template_button_click_17_listener", "DataandpropertiesComponent_div_41_Template_button_click_21_listener", "DataandpropertiesComponent_div_41_Template_button_click_25_listener", "DataandpropertiesComponent_div_41_Template_button_click_29_listener", "DataandpropertiesComponent_div_41_Template_button_click_33_listener", "DataandpropertiesComponent_div_41_Template_button_click_37_listener", "DataandpropertiesComponent_div_41_Template_button_click_41_listener", "DataandpropertiesComponent_div_41_Template_button_click_45_listener", "DataandpropertiesComponent_div_41_Template_button_click_52_listener", "DataandpropertiesComponent_div_41_Template_button_click_56_listener", "DataandpropertiesComponent_div_41_Template_button_click_63_listener", "DataandpropertiesComponent_div_41_Template_button_click_67_listener", "ɵɵadvance", "ɵɵproperty", "activeFilterButtons", "length", "ɵɵclassMap", "isFilterActive", "appliedFilters", "user", "role", "handleFileUpload", "bind", "downloadTemplate", "DataandpropertiesComponent_app_publish_property_card_44_Template_app_publish_property_card_backToTable_0_listener", "_r7", "onBackToTable", "DataandpropertiesComponent_app_success_adding_property_card_45_Template_app_success_adding_property_card_backToTable_0_listener", "_r8", "DataandpropertiesComponent", "unitService", "route", "cd", "fileInput", "showEmptyCard", "showSuccessCard", "showPublishCard", "isFilterDropdownVisible", "brokerId", "properties", "isLoading", "searchText", "searchTimeout", "currentCompoundType", "currentPropertyType", "constructor", "ngOnInit", "userJson", "localStorage", "getItem", "JSON", "parse", "checkRouteParams", "loadPropertiesCount", "getByBrokerId", "subscribe", "next", "response", "data", "updateCardVisibility", "detectChanges", "error", "console", "onSearchTextChange", "value", "clearTimeout", "setTimeout", "unitType", "trim", "toggleFilterDropdown", "filters", "log", "compoundType", "propertyType", "<PERSON><PERSON><PERSON>", "otherFilters", "includes", "getFieldsToShow", "type", "shouldShowField", "fieldName", "queryParams", "params", "hideCardsAfterDelay", "onFileSelected", "event", "file", "target", "files", "name", "_this", "uploadExcelUnits", "_ref", "_asyncToGenerator", "refreshTimestamp", "Date", "now", "nativeElement", "_x", "apply", "arguments", "fire", "downloadExcelTemplate", "blob", "err", "ɵɵdirectiveInject", "i1", "UnitService", "i2", "ActivatedRoute", "ChangeDetectorRef", "selectors", "viewQuery", "DataandpropertiesComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "DataandpropertiesComponent_Template_input_ngModelChange_15_listener", "_r1", "ɵɵtwoWayBindingSet", "DataandpropertiesComponent_Template_button_click_19_listener", "DataandpropertiesComponent_div_23_Template", "DataandpropertiesComponent_Template_input_change_24_listener", "DataandpropertiesComponent_Template_button_click_26_listener", "fileInput_r4", "ɵɵreference", "click", "DataandpropertiesComponent_Template_button_click_30_listener", "DataandpropertiesComponent_div_40_Template", "DataandpropertiesComponent_div_41_Template", "DataandpropertiesComponent_app_propertiestable_42_Template", "DataandpropertiesComponent_app_empty_properties_card_43_Template", "DataandpropertiesComponent_app_publish_property_card_44_Template", "DataandpropertiesComponent_app_success_adding_property_card_45_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c1"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\dataandproperties.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\dataandproperties.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild, ElementRef } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { UnitService } from '../services/unit.service';\r\nimport { saveAs } from 'file-saver';\r\nimport Swal from 'sweetalert2';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-dataandproperties',\r\n  templateUrl: './dataandproperties.component.html',\r\n  styleUrl: './dataandproperties.component.scss',\r\n})\r\nexport class DataandpropertiesComponent implements OnInit {\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n  showEmptyCard = false;\r\n  showSuccessCard = false;\r\n  showPublishCard = false;\r\n  isFilterDropdownVisible = false;\r\n\r\n  brokerId :any;\r\n  user: any;\r\n\r\n  properties: any[] = [];\r\n  isLoading = false;\r\n  appliedFilters: any = {};\r\n  searchText: string = '';\r\n  private searchTimeout: any;\r\n\r\n  // Filter properties\r\n  currentCompoundType: string = '';\r\n  currentPropertyType: string = '';\r\n  activeFilterButtons: string[] = [];\r\n\r\n  constructor(\r\n    private unitService: UnitService,\r\n    private route: ActivatedRoute,\r\n    private cd: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    const userJson = localStorage.getItem('currentUser');\r\n    this.user = userJson ? JSON.parse(userJson) : null;\r\n    this.brokerId= this.user?.brokerId;\r\n    this.checkRouteParams();\r\n    this.loadPropertiesCount();\r\n  }\r\n\r\n  loadPropertiesCount() {\r\n    this.isLoading = true;\r\n    this.unitService.getByBrokerId(this.brokerId).subscribe({\r\n      next: (response: any) => {\r\n        this.properties = response.data || [];\r\n        this.isLoading = false;\r\n        this.updateCardVisibility();\r\n        this.cd.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading properties:', error);\r\n        this.properties = [];\r\n        this.isLoading = false;\r\n        this.updateCardVisibility();\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  onSearchTextChange(value: string): void {\r\n    clearTimeout(this.searchTimeout);\r\n    this.searchTimeout = setTimeout(() => {\r\n      this.appliedFilters = {\r\n        ...this.appliedFilters,\r\n        unitType: value.trim(),\r\n      };\r\n      this.cd.detectChanges();\r\n    }, 300);\r\n  }\r\n\r\n  toggleFilterDropdown() {\r\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\r\n  }\r\n\r\n  onFiltersApplied(filters: any) {\r\n    console.log('Received filters:', filters);\r\n    this.toggleFilterDropdown();\r\n    this.appliedFilters = filters;\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  // Filter button methods\r\n  applyFilter(compoundType: string, propertyType: string) {\r\n    this.currentCompoundType = compoundType;\r\n    this.currentPropertyType = propertyType;\r\n\r\n    const filterKey = `${compoundType}_${propertyType}`;\r\n\r\n    // Update active filter buttons\r\n    this.activeFilterButtons = [filterKey];\r\n\r\n    // Apply filters to table\r\n    this.appliedFilters = {\r\n      ...this.appliedFilters,\r\n      compoundType: compoundType,\r\n      propertyType: propertyType,\r\n      filterKey: filterKey\r\n    };\r\n\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  clearFilters() {\r\n    this.currentCompoundType = '';\r\n    this.currentPropertyType = '';\r\n    this.activeFilterButtons = [];\r\n\r\n    // Remove compound and property type filters but keep other filters\r\n    const { compoundType, propertyType, filterKey, ...otherFilters } = this.appliedFilters;\r\n    this.appliedFilters = otherFilters;\r\n\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  isFilterActive(compoundType: string, propertyType: string): boolean {\r\n    const filterKey = `${compoundType}_${propertyType}`;\r\n    return this.activeFilterButtons.includes(filterKey);\r\n  }\r\n\r\n  // Get fields to show based on compound type and property type\r\n  getFieldsToShow(): any[] {\r\n    const compoundType = this.currentCompoundType;\r\n    const type = this.currentPropertyType;\r\n\r\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\r\n      return ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\r\n      return ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\r\n      return ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\r\n      return ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\r\n      return ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\r\n      return ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\r\n      return ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\r\n      return ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n    }\r\n    else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {\r\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'];\r\n    }\r\n    else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {\r\n      return ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];\r\n    }\r\n\r\n    return [];\r\n  }\r\n\r\n  // Check if a specific field should be shown\r\n  shouldShowField(fieldName: string): boolean {\r\n    return this.getFieldsToShow().includes(fieldName);\r\n  }\r\n\r\n  checkRouteParams() {\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['success'] === 'add') {\r\n        this.showSuccessCard = true;\r\n        this.hideCardsAfterDelay();\r\n      } else if (params['success'] === 'publish') {\r\n        this.showPublishCard = true;\r\n        this.hideCardsAfterDelay();\r\n      }\r\n    });\r\n  }\r\n\r\n  updateCardVisibility() {\r\n    this.showEmptyCard =\r\n      this.properties.length === 0 &&\r\n      !this.showSuccessCard &&\r\n      !this.showPublishCard;\r\n  }\r\n\r\n  hideCardsAfterDelay() {\r\n    setTimeout(() => {\r\n      this.showSuccessCard = false;\r\n      this.showPublishCard = false;\r\n      this.updateCardVisibility();\r\n    }, 5000);\r\n  }\r\n\r\n  onBackToTable() {\r\n    this.showSuccessCard = false;\r\n    this.showPublishCard = false;\r\n    this.updateCardVisibility();\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      console.log('File selected:', file.name);\r\n      this.handleFileUpload(file);\r\n    }\r\n  }\r\n\r\n  handleFileUpload(file: File) {\r\n    console.log('Uploading file:', file.name);\r\n    this.unitService.uploadExcelUnits(file, this.brokerId).subscribe({\r\n      next: async (response) => {\r\n        console.log('Upload successful:', response);\r\n        this.showEmptyCard = false;\r\n        this.appliedFilters = {\r\n          ...this.appliedFilters,\r\n          refreshTimestamp: Date.now(),\r\n        };\r\n        this.loadPropertiesCount();\r\n        this.cd.detectChanges();\r\n        // Reset file input\r\n        if (this.fileInput && this.fileInput.nativeElement) {\r\n          this.fileInput.nativeElement.value = '';\r\n        }\r\n        this.showSuccessCard = true;\r\n        this.hideCardsAfterDelay();\r\n      },\r\n      error: (error) => {\r\n        console.error('Upload error:', error);\r\n        Swal.fire('Error', 'Error uploading file. Please try again.', 'error');\r\n      },\r\n    });\r\n  }\r\n\r\n  downloadTemplate() {\r\n    this.unitService.downloadExcelTemplate().subscribe({\r\n      next: (blob: Blob) => {\r\n        saveAs(blob, 'units-template.xlsx');\r\n      },\r\n      error: (err) => console.error('Download error:', err),\r\n    });\r\n  }\r\n}\r\n", "<div class=\"mb-5 mt-0\">\r\n  <app-broker-title></app-broker-title>\r\n</div>\r\n\r\n<div class=\"card mb-5 mb-xl-10\">\r\n  <div class=\"card-body pt-3 pb-0\">\r\n    <!-- Header Section -->\r\n    <div class=\"row mb-6\">\r\n      <div class=\"col-12\">\r\n        <div\r\n          class=\"d-flex flex-column flex-lg-row align-items-start align-items-lg-center justify-content-between gap-3 mt-2\">\r\n\r\n          <!-- Left: Title Section -->\r\n          <div class=\"flex-shrink-0\">\r\n            <h1 class=\"text-dark-blue fs-2 fs-lg-1 fw-bolder mb-1\">\r\n              Data and Properties\r\n            </h1>\r\n            <p class=\"text-muted fs-6 mb-0\">\r\n              View and manage your property data\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Center: Search -->\r\n          <div class=\"flex-grow-1 mx-lg-4\" style=\"max-width: 400px;\">\r\n            <div class=\"position-relative\">\r\n              <app-keenicon name=\"magnifier\" class=\"fs-3 text-gray-500 position-absolute top-50 translate-middle-y ms-4\"\r\n                type=\"outline\">\r\n              </app-keenicon>\r\n              <input type=\"text\" name=\"searchText\"\r\n                class=\"form-control form-control-lg ps-12 bg-light border-0 rounded-3\" [(ngModel)]=\"searchText\"\r\n                (ngModelChange)=\"onSearchTextChange($event)\" placeholder=\"Search by unit type...\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Right: Action Buttons -->\r\n          <div class=\"flex-shrink-0\">\r\n            <div class=\"d-flex flex-column flex-sm-row gap-2\">\r\n\r\n              <!-- Filter Button -->\r\n              <div class=\"position-relative\">\r\n                <button type=\"button\" class=\"btn btn-light-primary btn-sm px-3 py-2\" (click)=\"toggleFilterDropdown()\">\r\n                  <i class=\"fa-solid fa-filter me-2\"></i>\r\n                  <span class=\"d-none d-md-inline\">Filter</span>\r\n                </button>\r\n\r\n                <!-- Filter Dropdown -->\r\n                <div *ngIf=\"isFilterDropdownVisible\" class=\"dropdown-menu show p-3 shadow-lg border-0 rounded-3\" style=\"\r\n                    position: absolute;\r\n                    top: 100%;\r\n                    right: 0;\r\n                    z-index: 1000;\r\n                    min-width: 280px;\r\n                    max-width: 90vw;\r\n                  \">\r\n                  <app-unit-filter (filtersApplied)=\"onFiltersApplied($event)\"></app-unit-filter>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Upload Units -->\r\n              <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" accept=\".xlsx,.xls\" hidden />\r\n              <button type=\"button\" class=\"btn btn-light-success btn-sm px-3 py-2\" (click)=\"fileInput.click()\">\r\n                <i class=\"fa-solid fa-upload me-2\"></i>\r\n                <span class=\"d-none d-md-inline\">Upload</span>\r\n              </button>\r\n\r\n              <!-- Download Template -->\r\n              <button type=\"button\" class=\"btn btn-light-info btn-sm px-3 py-2\" (click)=\"downloadTemplate()\">\r\n                <i class=\"fa-solid fa-download me-2\"></i>\r\n                <span class=\"d-none d-md-inline\">Template</span>\r\n              </button>\r\n\r\n              <!-- Add New Property -->\r\n              <button type=\"button\" class=\"btn btn-primary btn-sm px-3 py-2 fw-bold\"\r\n                [routerLink]=\"['/broker/add-property']\">\r\n                <i class=\"fa-solid fa-plus me-2\"></i>\r\n                <span class=\"d-none d-lg-inline\">Add Unit</span>\r\n                <span class=\"d-lg-none\">Add</span>\r\n              </button>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"isLoading\" class=\"d-flex justify-content-center align-items-center py-10\">\r\n      <div class=\"spinner-border text-primary\" role=\"status\">\r\n        <span class=\"visually-hidden\">Loading...</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Filter Buttons Section -->\r\n    <div *ngIf=\"!isLoading && !showEmptyCard && !showSuccessCard && !showPublishCard\" class=\"mb-4\">\r\n      <div class=\"card border-0 shadow-sm\">\r\n        <div class=\"card-body p-4\">\r\n          <div class=\"d-flex flex-wrap align-items-center justify-content-between mb-3\">\r\n            <h5 class=\"text-dark-blue fw-bold mb-0\">\r\n              <i class=\"fa-solid fa-filter me-2\"></i>\r\n              Property Filters\r\n            </h5>\r\n            <button class=\"btn btn-sm btn-light-danger\" (click)=\"clearFilters()\" *ngIf=\"activeFilterButtons.length > 0\">\r\n              <i class=\"fa-solid fa-times me-1\"></i>\r\n              Clear All Filters\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Filter Buttons Grid -->\r\n          <div class=\"row g-2\">\r\n            <!-- Outside Compound Filters -->\r\n            <div class=\"col-12\">\r\n              <h6 class=\"text-muted fw-semibold mb-2\">Outside Compound</h6>\r\n            </div>\r\n\r\n            <!-- Residential Units -->\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'apartments') ? 'btn-primary' : 'btn-light-primary'\"\r\n                (click)=\"applyFilter('outside_compound', 'apartments')\">\r\n                <i class=\"fa-solid fa-building me-1\"></i>\r\n                Apartments\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'duplexes') ? 'btn-primary' : 'btn-light-primary'\"\r\n                (click)=\"applyFilter('outside_compound', 'duplexes')\">\r\n                <i class=\"fa-solid fa-home me-1\"></i>\r\n                Duplexes\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'studios') ? 'btn-primary' : 'btn-light-primary'\"\r\n                (click)=\"applyFilter('outside_compound', 'studios')\">\r\n                <i class=\"fa-solid fa-door-open me-1\"></i>\r\n                Studios\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'penthouses') ? 'btn-primary' : 'btn-light-primary'\"\r\n                (click)=\"applyFilter('outside_compound', 'penthouses')\">\r\n                <i class=\"fa-solid fa-crown me-1\"></i>\r\n                Penthouses\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'villas') ? 'btn-primary' : 'btn-light-primary'\"\r\n                (click)=\"applyFilter('outside_compound', 'villas')\">\r\n                <i class=\"fa-solid fa-house me-1\"></i>\r\n                Villas\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Commercial Units -->\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'commercial_stores') ? 'btn-success' : 'btn-light-success'\"\r\n                (click)=\"applyFilter('outside_compound', 'commercial_stores')\">\r\n                <i class=\"fa-solid fa-store me-1\"></i>\r\n                Commercial Stores\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'warehouses') ? 'btn-success' : 'btn-light-success'\"\r\n                (click)=\"applyFilter('outside_compound', 'warehouses')\">\r\n                <i class=\"fa-solid fa-warehouse me-1\"></i>\r\n                Warehouses\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'factories') ? 'btn-success' : 'btn-light-success'\"\r\n                (click)=\"applyFilter('outside_compound', 'factories')\">\r\n                <i class=\"fa-solid fa-industry me-1\"></i>\r\n                Factories\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Land Types -->\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('outside_compound', 'residential_villa_lands') ? 'btn-warning' : 'btn-light-warning'\"\r\n                (click)=\"applyFilter('outside_compound', 'residential_villa_lands')\">\r\n                <i class=\"fa-solid fa-map me-1\"></i>\r\n                Villa Lands\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Inside Compound Filters -->\r\n            <div class=\"col-12 mt-3\">\r\n              <h6 class=\"text-muted fw-semibold mb-2\">Inside Compound</h6>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('inside_compound', 'apartments') ? 'btn-info' : 'btn-light-info'\"\r\n                (click)=\"applyFilter('inside_compound', 'apartments')\">\r\n                <i class=\"fa-solid fa-building me-1\"></i>\r\n                Compound Apartments\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('inside_compound', 'standalone_villas') ? 'btn-info' : 'btn-light-info'\"\r\n                (click)=\"applyFilter('inside_compound', 'standalone_villas')\">\r\n                <i class=\"fa-solid fa-house me-1\"></i>\r\n                Standalone Villas\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Village Filters -->\r\n            <div class=\"col-12 mt-3\">\r\n              <h6 class=\"text-muted fw-semibold mb-2\">Village (Rent Only)</h6>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('village', 'apartments') ? 'btn-secondary' : 'btn-light-secondary'\"\r\n                (click)=\"applyFilter('village', 'apartments')\">\r\n                <i class=\"fa-solid fa-calendar-days me-1\"></i>\r\n                Village Apartments\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"col-lg-3 col-md-4 col-sm-6\">\r\n              <button class=\"btn btn-sm w-100 fw-medium\"\r\n                [class]=\"isFilterActive('village', 'chalets') ? 'btn-secondary' : 'btn-light-secondary'\"\r\n                (click)=\"applyFilter('village', 'chalets')\">\r\n                <i class=\"fa-solid fa-umbrella-beach me-1\"></i>\r\n                Chalets\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Properties Table -->\r\n    <app-propertiestable *ngIf=\"\r\n        !isLoading && !showEmptyCard && !showSuccessCard && !showPublishCard\r\n      \" [appliedFilters]=\"appliedFilters\">\r\n    </app-propertiestable>\r\n\r\n    <!-- Empty Properties Card -->\r\n    <app-empty-properties-card *ngIf=\"!isLoading && showEmptyCard\" [userRole]=\"user.role\"\r\n      [onFileUpload]=\"handleFileUpload.bind(this)\" [onDownloadTemplate]=\"downloadTemplate.bind(this)\">\r\n    </app-empty-properties-card>\r\n\r\n    <!-- Publish Property Card -->\r\n    <app-publish-property-card *ngIf=\"!isLoading && showPublishCard\" (backToTable)=\"onBackToTable()\">\r\n    </app-publish-property-card>\r\n\r\n    <!-- Success Adding Property Card -->\r\n    <app-success-adding-property-card *ngIf=\"!isLoading && showSuccessCard\" (backToTable)=\"onBackToTable()\">\r\n    </app-success-adding-property-card>\r\n  </div>\r\n</div>"], "mappings": ";AAGA,SAASA,MAAM,QAAQ,YAAY;AACnC,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;;ICkDZC,EARF,CAAAC,cAAA,cAOI,0BAC2D;IAA5CD,EAAA,CAAAE,UAAA,4BAAAC,qFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAkBF,MAAA,CAAAG,gBAAA,CAAAN,MAAA,CAAwB;IAAA,EAAC;IAC9DJ,EAD+D,CAAAW,YAAA,EAAkB,EAC3E;;;;;IAiCdX,EAFJ,CAAAC,cAAA,cAAsF,cAC7B,eACvB;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IAE5CZ,EAF4C,CAAAW,YAAA,EAAO,EAC3C,EACF;;;;;;IAWEX,EAAA,CAAAC,cAAA,iBAA4G;IAAhED,EAAA,CAAAE,UAAA,mBAAAW,4EAAA;MAAAb,EAAA,CAAAK,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAClEf,EAAA,CAAAgB,SAAA,YAAsC;IACtChB,EAAA,CAAAY,MAAA,0BACF;IAAAZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAPTX,EAJR,CAAAC,cAAA,cAA+F,cACxD,cACR,cACqD,aACpC;IACtCD,EAAA,CAAAgB,SAAA,YAAuC;IACvChB,EAAA,CAAAY,MAAA,yBACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAiB,UAAA,IAAAC,mDAAA,qBAA4G;IAI9GlB,EAAA,CAAAW,YAAA,EAAM;IAMFX,EAHJ,CAAAC,cAAA,cAAqB,aAEC,cACsB;IAAAD,EAAA,CAAAY,MAAA,wBAAgB;IAC1DZ,EAD0D,CAAAW,YAAA,EAAK,EACzD;IAIJX,EADF,CAAAC,cAAA,eAAwC,kBAGoB;IAAxDD,EAAA,CAAAE,UAAA,mBAAAiB,oEAAA;MAAAnB,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,YAAY,CAAC;IAAA,EAAC;IACvDrB,EAAA,CAAAgB,SAAA,aAAyC;IACzChB,EAAA,CAAAY,MAAA,oBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGkB;IAAtDD,EAAA,CAAAE,UAAA,mBAAAoB,oEAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,UAAU,CAAC;IAAA,EAAC;IACrDrB,EAAA,CAAAgB,SAAA,aAAqC;IACrChB,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGiB;IAArDD,EAAA,CAAAE,UAAA,mBAAAqB,oEAAA;MAAAvB,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,SAAS,CAAC;IAAA,EAAC;IACpDrB,EAAA,CAAAgB,SAAA,aAA0C;IAC1ChB,EAAA,CAAAY,MAAA,iBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGoB;IAAxDD,EAAA,CAAAE,UAAA,mBAAAsB,oEAAA;MAAAxB,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,YAAY,CAAC;IAAA,EAAC;IACvDrB,EAAA,CAAAgB,SAAA,aAAsC;IACtChB,EAAA,CAAAY,MAAA,oBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGgB;IAApDD,EAAA,CAAAE,UAAA,mBAAAuB,oEAAA;MAAAzB,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,QAAQ,CAAC;IAAA,EAAC;IACnDrB,EAAA,CAAAgB,SAAA,aAAsC;IACtChB,EAAA,CAAAY,MAAA,gBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAIJX,EADF,CAAAC,cAAA,eAAwC,kBAG2B;IAA/DD,EAAA,CAAAE,UAAA,mBAAAwB,oEAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,mBAAmB,CAAC;IAAA,EAAC;IAC9DrB,EAAA,CAAAgB,SAAA,aAAsC;IACtChB,EAAA,CAAAY,MAAA,2BACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGoB;IAAxDD,EAAA,CAAAE,UAAA,mBAAAyB,oEAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,YAAY,CAAC;IAAA,EAAC;IACvDrB,EAAA,CAAAgB,SAAA,aAA0C;IAC1ChB,EAAA,CAAAY,MAAA,oBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGmB;IAAvDD,EAAA,CAAAE,UAAA,mBAAA0B,oEAAA;MAAA5B,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,WAAW,CAAC;IAAA,EAAC;IACtDrB,EAAA,CAAAgB,SAAA,aAAyC;IACzChB,EAAA,CAAAY,MAAA,mBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAIJX,EADF,CAAAC,cAAA,eAAwC,kBAGiC;IAArED,EAAA,CAAAE,UAAA,mBAAA2B,oEAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,kBAAkB,EAAE,yBAAyB,CAAC;IAAA,EAAC;IACpErB,EAAA,CAAAgB,SAAA,aAAoC;IACpChB,EAAA,CAAAY,MAAA,qBACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAIJX,EADF,CAAAC,cAAA,eAAyB,cACiB;IAAAD,EAAA,CAAAY,MAAA,uBAAe;IACzDZ,EADyD,CAAAW,YAAA,EAAK,EACxD;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGmB;IAAvDD,EAAA,CAAAE,UAAA,mBAAA4B,oEAAA;MAAA9B,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,iBAAiB,EAAE,YAAY,CAAC;IAAA,EAAC;IACtDrB,EAAA,CAAAgB,SAAA,aAAyC;IACzChB,EAAA,CAAAY,MAAA,6BACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAG0B;IAA9DD,EAAA,CAAAE,UAAA,mBAAA6B,oEAAA;MAAA/B,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,iBAAiB,EAAE,mBAAmB,CAAC;IAAA,EAAC;IAC7DrB,EAAA,CAAAgB,SAAA,aAAsC;IACtChB,EAAA,CAAAY,MAAA,2BACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAIJX,EADF,CAAAC,cAAA,eAAyB,cACiB;IAAAD,EAAA,CAAAY,MAAA,2BAAmB;IAC7DZ,EAD6D,CAAAW,YAAA,EAAK,EAC5D;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGW;IAA/CD,EAAA,CAAAE,UAAA,mBAAA8B,oEAAA;MAAAhC,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,SAAS,EAAE,YAAY,CAAC;IAAA,EAAC;IAC9CrB,EAAA,CAAAgB,SAAA,aAA8C;IAC9ChB,EAAA,CAAAY,MAAA,4BACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,eAAwC,kBAGQ;IAA5CD,EAAA,CAAAE,UAAA,mBAAA+B,oEAAA;MAAAjC,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,WAAA,CAAY,SAAS,EAAE,SAAS,CAAC;IAAA,EAAC;IAC3CrB,EAAA,CAAAgB,SAAA,aAA+C;IAC/ChB,EAAA,CAAAY,MAAA,iBACF;IAKVZ,EALU,CAAAW,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;;;;IAjJwEX,EAAA,CAAAkC,SAAA,GAAoC;IAApClC,EAAA,CAAAmC,UAAA,SAAA5B,MAAA,CAAA6B,mBAAA,CAAAC,MAAA,KAAoC;IAgBtGrC,EAAA,CAAAkC,SAAA,GAAgG;IAAhGlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,yEAAgG;IAShGvC,EAAA,CAAAkC,SAAA,GAA8F;IAA9FlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,uEAA8F;IAS9FvC,EAAA,CAAAkC,SAAA,GAA6F;IAA7FlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,sEAA6F;IAS7FvC,EAAA,CAAAkC,SAAA,GAAgG;IAAhGlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,yEAAgG;IAShGvC,EAAA,CAAAkC,SAAA,GAA4F;IAA5FlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,qEAA4F;IAU5FvC,EAAA,CAAAkC,SAAA,GAAuG;IAAvGlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,gFAAuG;IASvGvC,EAAA,CAAAkC,SAAA,GAAgG;IAAhGlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,yEAAgG;IAShGvC,EAAA,CAAAkC,SAAA,GAA+F;IAA/FlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,wEAA+F;IAU/FvC,EAAA,CAAAkC,SAAA,GAA6G;IAA7GlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,sFAA6G;IAc7GvC,EAAA,CAAAkC,SAAA,GAAyF;IAAzFlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,kEAAyF;IASzFvC,EAAA,CAAAkC,SAAA,GAAgG;IAAhGlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,yEAAgG;IAchGvC,EAAA,CAAAkC,SAAA,GAA2F;IAA3FlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,oEAA2F;IAS3FvC,EAAA,CAAAkC,SAAA,GAAwF;IAAxFlC,EAAA,CAAAsC,UAAA,CAAA/B,MAAA,CAAAgC,cAAA,iEAAwF;;;;;IAYpGvC,EAAA,CAAAgB,SAAA,8BAGsB;;;;IADlBhB,EAAA,CAAAmC,UAAA,mBAAA5B,MAAA,CAAAiC,cAAA,CAAiC;;;;;IAIrCxC,EAAA,CAAAgB,SAAA,oCAE4B;;;;IADmBhB,EADgB,CAAAmC,UAAA,aAAA5B,MAAA,CAAAkC,IAAA,CAAAC,IAAA,CAAsB,iBAAAnC,MAAA,CAAAoC,gBAAA,CAAAC,IAAA,CAAArC,MAAA,EACvC,uBAAAA,MAAA,CAAAsC,gBAAA,CAAAD,IAAA,CAAArC,MAAA,EAAmD;;;;;;IAIjGP,EAAA,CAAAC,cAAA,oCAAiG;IAAhCD,EAAA,CAAAE,UAAA,yBAAA4C,kHAAA;MAAA9C,EAAA,CAAAK,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAeF,MAAA,CAAAyC,aAAA,EAAe;IAAA,EAAC;IAChGhD,EAAA,CAAAW,YAAA,EAA4B;;;;;;IAG5BX,EAAA,CAAAC,cAAA,2CAAwG;IAAhCD,EAAA,CAAAE,UAAA,yBAAA+C,gIAAA;MAAAjD,EAAA,CAAAK,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAeF,MAAA,CAAAyC,aAAA,EAAe;IAAA,EAAC;IACvGhD,EAAA,CAAAW,YAAA,EAAmC;;;AD7PvC,OAAM,MAAOwC,0BAA0B;EAuB3BC,WAAA;EACAC,KAAA;EACAC,EAAA;EAxBcC,SAAS;EAEjCC,aAAa,GAAG,KAAK;EACrBC,eAAe,GAAG,KAAK;EACvBC,eAAe,GAAG,KAAK;EACvBC,uBAAuB,GAAG,KAAK;EAE/BC,QAAQ;EACRnB,IAAI;EAEJoB,UAAU,GAAU,EAAE;EACtBC,SAAS,GAAG,KAAK;EACjBtB,cAAc,GAAQ,EAAE;EACxBuB,UAAU,GAAW,EAAE;EACfC,aAAa;EAErB;EACAC,mBAAmB,GAAW,EAAE;EAChCC,mBAAmB,GAAW,EAAE;EAChC9B,mBAAmB,GAAa,EAAE;EAElC+B,YACUf,WAAwB,EACxBC,KAAqB,EACrBC,EAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;EACT;EAEHc,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAI,CAAC9B,IAAI,GAAG4B,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAClD,IAAI,CAACT,QAAQ,GAAE,IAAI,CAACnB,IAAI,EAAEmB,QAAQ;IAClC,IAAI,CAACc,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,CAACb,SAAS,GAAG,IAAI;IACrB,IAAI,CAACV,WAAW,CAACwB,aAAa,CAAC,IAAI,CAAChB,QAAQ,CAAC,CAACiB,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAClB,UAAU,GAAGkB,QAAQ,CAACC,IAAI,IAAI,EAAE;QACrC,IAAI,CAAClB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACmB,oBAAoB,EAAE;QAC3B,IAAI,CAAC3B,EAAE,CAAC4B,aAAa,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACtB,UAAU,GAAG,EAAE;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACmB,oBAAoB,EAAE;QAC3B,IAAI,CAAC3B,EAAE,CAAC4B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEAG,kBAAkBA,CAACC,KAAa;IAC9BC,YAAY,CAAC,IAAI,CAACvB,aAAa,CAAC;IAChC,IAAI,CAACA,aAAa,GAAGwB,UAAU,CAAC,MAAK;MACnC,IAAI,CAAChD,cAAc,GAAG;QACpB,GAAG,IAAI,CAACA,cAAc;QACtBiD,QAAQ,EAAEH,KAAK,CAACI,IAAI;OACrB;MACD,IAAI,CAACpC,EAAE,CAAC4B,aAAa,EAAE;IACzB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAS,oBAAoBA,CAAA;IAClB,IAAI,CAAChC,uBAAuB,GAAG,CAAC,IAAI,CAACA,uBAAuB;EAC9D;EAEAjD,gBAAgBA,CAACkF,OAAY;IAC3BR,OAAO,CAACS,GAAG,CAAC,mBAAmB,EAAED,OAAO,CAAC;IACzC,IAAI,CAACD,oBAAoB,EAAE;IAC3B,IAAI,CAACnD,cAAc,GAAGoD,OAAO;IAC7B,IAAI,CAACtC,EAAE,CAAC4B,aAAa,EAAE;EACzB;EAEA;EACA7D,WAAWA,CAACyE,YAAoB,EAAEC,YAAoB;IACpD,IAAI,CAAC9B,mBAAmB,GAAG6B,YAAY;IACvC,IAAI,CAAC5B,mBAAmB,GAAG6B,YAAY;IAEvC,MAAMC,SAAS,GAAG,GAAGF,YAAY,IAAIC,YAAY,EAAE;IAEnD;IACA,IAAI,CAAC3D,mBAAmB,GAAG,CAAC4D,SAAS,CAAC;IAEtC;IACA,IAAI,CAACxD,cAAc,GAAG;MACpB,GAAG,IAAI,CAACA,cAAc;MACtBsD,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BC,SAAS,EAAEA;KACZ;IAED,IAAI,CAAC1C,EAAE,CAAC4B,aAAa,EAAE;EACzB;EAEAnE,YAAYA,CAAA;IACV,IAAI,CAACkD,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC9B,mBAAmB,GAAG,EAAE;IAE7B;IACA,MAAM;MAAE0D,YAAY;MAAEC,YAAY;MAAEC,SAAS;MAAE,GAAGC;IAAY,CAAE,GAAG,IAAI,CAACzD,cAAc;IACtF,IAAI,CAACA,cAAc,GAAGyD,YAAY;IAElC,IAAI,CAAC3C,EAAE,CAAC4B,aAAa,EAAE;EACzB;EAEA3C,cAAcA,CAACuD,YAAoB,EAAEC,YAAoB;IACvD,MAAMC,SAAS,GAAG,GAAGF,YAAY,IAAIC,YAAY,EAAE;IACnD,OAAO,IAAI,CAAC3D,mBAAmB,CAAC8D,QAAQ,CAACF,SAAS,CAAC;EACrD;EAEA;EACAG,eAAeA,CAAA;IACb,MAAML,YAAY,GAAG,IAAI,CAAC7B,mBAAmB;IAC7C,MAAMmC,IAAI,GAAG,IAAI,CAAClC,mBAAmB;IAErC,IAAI4B,YAAY,KAAK,kBAAkB,KAAKM,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,UAAU,CAAC,EAAE;MACnL,OAAO,CAAC,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACrT,CAAC,MACI,IAAIN,YAAY,KAAK,kBAAkB,KAAKM,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,gBAAgB,CAAC,EAAE;MAC9F,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IAC3S,CAAC,MACI,IAAIN,YAAY,KAAK,kBAAkB,KAAKM,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAC,EAAE;MACxK,OAAO,CAAC,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IAChT,CAAC,MACI,IAAIN,YAAY,KAAK,kBAAkB,KAAKM,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,CAAC,EAAE;MAC/F,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,iBAAiB,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACnR,CAAC,MACI,IAAIN,YAAY,KAAK,kBAAkB,KAAKM,IAAI,KAAK,yBAAyB,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,iCAAiC,IAAIA,IAAI,KAAK,kBAAkB,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,kBAAkB,CAAC,EAAE;MACvV,OAAO,CAAC,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACpS,CAAC,MACI,IAAIN,YAAY,KAAK,iBAAiB,KAAKM,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,SAAS,CAAC,EAAE;MAClK,OAAO,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IAC5U,CAAC,MACI,IAAIN,YAAY,KAAK,iBAAiB,KAAKM,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,CAAC,EAAE;MACjI,OAAO,CAAC,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACzV,CAAC,MACI,IAAIN,YAAY,KAAK,iBAAiB,KAAKM,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAC,EAAE;MACvK,OAAO,CAAC,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACrU,CAAC,MACI,IAAIN,YAAY,KAAK,SAAS,KAAKM,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,gBAAgB,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,SAAS,CAAC,EAAE;MACjW,OAAO,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;IACjO,CAAC,MACI,IAAIN,YAAY,KAAK,SAAS,KAAKM,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,YAAY,CAAC,EAAE;MAChN,OAAO,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,CAAC;IAC/N;IAEA,OAAO,EAAE;EACX;EAEA;EACAC,eAAeA,CAACC,SAAiB;IAC/B,OAAO,IAAI,CAACH,eAAe,EAAE,CAACD,QAAQ,CAACI,SAAS,CAAC;EACnD;EAEA5B,gBAAgBA,CAAA;IACd,IAAI,CAACrB,KAAK,CAACkD,WAAW,CAAC1B,SAAS,CAAE2B,MAAM,IAAI;MAC1C,IAAIA,MAAM,CAAC,SAAS,CAAC,KAAK,KAAK,EAAE;QAC/B,IAAI,CAAC/C,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACgD,mBAAmB,EAAE;MAC5B,CAAC,MAAM,IAAID,MAAM,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE;QAC1C,IAAI,CAAC9C,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC+C,mBAAmB,EAAE;MAC5B;IACF,CAAC,CAAC;EACJ;EAEAxB,oBAAoBA,CAAA;IAClB,IAAI,CAACzB,aAAa,GAChB,IAAI,CAACK,UAAU,CAACxB,MAAM,KAAK,CAAC,IAC5B,CAAC,IAAI,CAACoB,eAAe,IACrB,CAAC,IAAI,CAACC,eAAe;EACzB;EAEA+C,mBAAmBA,CAAA;IACjBjB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC/B,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACuB,oBAAoB,EAAE;IAC7B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAjC,aAAaA,CAAA;IACX,IAAI,CAACS,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACuB,oBAAoB,EAAE;IAC3B,IAAI,CAAC3B,EAAE,CAAC4B,aAAa,EAAE;EACzB;EAEAwB,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRxB,OAAO,CAACS,GAAG,CAAC,gBAAgB,EAAEe,IAAI,CAACG,IAAI,CAAC;MACxC,IAAI,CAACpE,gBAAgB,CAACiE,IAAI,CAAC;IAC7B;EACF;EAEAjE,gBAAgBA,CAACiE,IAAU;IAAA,IAAAI,KAAA;IACzB5B,OAAO,CAACS,GAAG,CAAC,iBAAiB,EAAEe,IAAI,CAACG,IAAI,CAAC;IACzC,IAAI,CAAC3D,WAAW,CAAC6D,gBAAgB,CAACL,IAAI,EAAE,IAAI,CAAChD,QAAQ,CAAC,CAACiB,SAAS,CAAC;MAC/DC,IAAI;QAAA,IAAAoC,IAAA,GAAAC,iBAAA,CAAE,WAAOpC,QAAQ,EAAI;UACvBK,OAAO,CAACS,GAAG,CAAC,oBAAoB,EAAEd,QAAQ,CAAC;UAC3CiC,KAAI,CAACxD,aAAa,GAAG,KAAK;UAC1BwD,KAAI,CAACxE,cAAc,GAAG;YACpB,GAAGwE,KAAI,CAACxE,cAAc;YACtB4E,gBAAgB,EAAEC,IAAI,CAACC,GAAG;WAC3B;UACDN,KAAI,CAACrC,mBAAmB,EAAE;UAC1BqC,KAAI,CAAC1D,EAAE,CAAC4B,aAAa,EAAE;UACvB;UACA,IAAI8B,KAAI,CAACzD,SAAS,IAAIyD,KAAI,CAACzD,SAAS,CAACgE,aAAa,EAAE;YAClDP,KAAI,CAACzD,SAAS,CAACgE,aAAa,CAACjC,KAAK,GAAG,EAAE;UACzC;UACA0B,KAAI,CAACvD,eAAe,GAAG,IAAI;UAC3BuD,KAAI,CAACP,mBAAmB,EAAE;QAC5B,CAAC;QAAA,gBAfD3B,IAAIA,CAAA0C,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA,GAeH;MACDvC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrCpF,IAAI,CAAC4H,IAAI,CAAC,OAAO,EAAE,yCAAyC,EAAE,OAAO,CAAC;MACxE;KACD,CAAC;EACJ;EAEA9E,gBAAgBA,CAAA;IACd,IAAI,CAACO,WAAW,CAACwE,qBAAqB,EAAE,CAAC/C,SAAS,CAAC;MACjDC,IAAI,EAAG+C,IAAU,IAAI;QACnB/H,MAAM,CAAC+H,IAAI,EAAE,qBAAqB,CAAC;MACrC,CAAC;MACD1C,KAAK,EAAG2C,GAAG,IAAK1C,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAE2C,GAAG;KACrD,CAAC;EACJ;;qCA1OW3E,0BAA0B,EAAAnD,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnI,EAAA,CAAA+H,iBAAA,CAAA/H,EAAA,CAAAoI,iBAAA;EAAA;;UAA1BjF,0BAA0B;IAAAkF,SAAA;IAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QCZvCxI,EAAA,CAAAC,cAAA,aAAuB;QACrBD,EAAA,CAAAgB,SAAA,uBAAqC;QACvChB,EAAA,CAAAW,YAAA,EAAM;QAYMX,EAVZ,CAAAC,cAAA,aAAgC,aACG,aAET,aACA,aAEkG,aAGvF,YAC8B;QACrDD,EAAA,CAAAY,MAAA,4BACF;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACLX,EAAA,CAAAC,cAAA,YAAgC;QAC9BD,EAAA,CAAAY,MAAA,4CACF;QACFZ,EADE,CAAAW,YAAA,EAAI,EACA;QAIJX,EADF,CAAAC,cAAA,eAA2D,eAC1B;QAC7BD,EAAA,CAAAgB,SAAA,wBAEe;QACfhB,EAAA,CAAAC,cAAA,iBAEsF;QADbD,EAAA,CAAA0I,gBAAA,2BAAAC,oEAAAvI,MAAA;UAAAJ,EAAA,CAAAK,aAAA,CAAAuI,GAAA;UAAA5I,EAAA,CAAA6I,kBAAA,CAAAJ,GAAA,CAAA1E,UAAA,EAAA3D,MAAA,MAAAqI,GAAA,CAAA1E,UAAA,GAAA3D,MAAA;UAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;QAAA,EAAwB;QAC/FJ,EAAA,CAAAE,UAAA,2BAAAyI,oEAAAvI,MAAA;UAAAJ,EAAA,CAAAK,aAAA,CAAAuI,GAAA;UAAA,OAAA5I,EAAA,CAAAS,WAAA,CAAiBgI,GAAA,CAAApD,kBAAA,CAAAjF,MAAA,CAA0B;QAAA,EAAC;QAElDJ,EAJI,CAAAW,YAAA,EAEsF,EAClF,EACF;QAQAX,EALN,CAAAC,cAAA,cAA2B,eACyB,eAGjB,kBACyE;QAAjCD,EAAA,CAAAE,UAAA,mBAAA4I,6DAAA;UAAA9I,EAAA,CAAAK,aAAA,CAAAuI,GAAA;UAAA,OAAA5I,EAAA,CAAAS,WAAA,CAASgI,GAAA,CAAA9C,oBAAA,EAAsB;QAAA,EAAC;QACnG3F,EAAA,CAAAgB,SAAA,aAAuC;QACvChB,EAAA,CAAAC,cAAA,gBAAiC;QAAAD,EAAA,CAAAY,MAAA,cAAM;QACzCZ,EADyC,CAAAW,YAAA,EAAO,EACvC;QAGTX,EAAA,CAAAiB,UAAA,KAAA8H,0CAAA,kBAOI;QAGN/I,EAAA,CAAAW,YAAA,EAAM;QAGNX,EAAA,CAAAC,cAAA,oBAA6F;QAA/DD,EAAA,CAAAE,UAAA,oBAAA8I,6DAAA5I,MAAA;UAAAJ,EAAA,CAAAK,aAAA,CAAAuI,GAAA;UAAA,OAAA5I,EAAA,CAAAS,WAAA,CAAUgI,GAAA,CAAA/B,cAAA,CAAAtG,MAAA,CAAsB;QAAA,EAAC;QAA/DJ,EAAA,CAAAW,YAAA,EAA6F;QAC7FX,EAAA,CAAAC,cAAA,kBAAiG;QAA5BD,EAAA,CAAAE,UAAA,mBAAA+I,6DAAA;UAAAjJ,EAAA,CAAAK,aAAA,CAAAuI,GAAA;UAAA,MAAAM,YAAA,GAAAlJ,EAAA,CAAAmJ,WAAA;UAAA,OAAAnJ,EAAA,CAAAS,WAAA,CAASyI,YAAA,CAAAE,KAAA,EAAiB;QAAA,EAAC;QAC9FpJ,EAAA,CAAAgB,SAAA,aAAuC;QACvChB,EAAA,CAAAC,cAAA,gBAAiC;QAAAD,EAAA,CAAAY,MAAA,cAAM;QACzCZ,EADyC,CAAAW,YAAA,EAAO,EACvC;QAGTX,EAAA,CAAAC,cAAA,kBAA+F;QAA7BD,EAAA,CAAAE,UAAA,mBAAAmJ,6DAAA;UAAArJ,EAAA,CAAAK,aAAA,CAAAuI,GAAA;UAAA,OAAA5I,EAAA,CAAAS,WAAA,CAASgI,GAAA,CAAA5F,gBAAA,EAAkB;QAAA,EAAC;QAC5F7C,EAAA,CAAAgB,SAAA,aAAyC;QACzChB,EAAA,CAAAC,cAAA,gBAAiC;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAC3CZ,EAD2C,CAAAW,YAAA,EAAO,EACzC;QAGTX,EAAA,CAAAC,cAAA,kBAC0C;QACxCD,EAAA,CAAAgB,SAAA,aAAqC;QACrChB,EAAA,CAAAC,cAAA,gBAAiC;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QAChDX,EAAA,CAAAC,cAAA,gBAAwB;QAAAD,EAAA,CAAAY,MAAA,WAAG;QAOvCZ,EAPuC,CAAAW,YAAA,EAAO,EAC3B,EAEL,EACF,EACF,EACF,EACF;QAqLNX,EAlLA,CAAAiB,UAAA,KAAAqI,0CAAA,kBAAsF,KAAAC,0CAAA,oBAOS,KAAAC,0DAAA,kCA8JzD,KAAAC,gEAAA,wCAK4D,KAAAC,gEAAA,wCAID,KAAAC,uEAAA,+CAIO;QAG5G3J,EADE,CAAAW,YAAA,EAAM,EACF;;;QA9OiFX,EAAA,CAAAkC,SAAA,IAAwB;QAAxBlC,EAAA,CAAA4J,gBAAA,YAAAnB,GAAA,CAAA1E,UAAA,CAAwB;QAiBzF/D,EAAA,CAAAkC,SAAA,GAA6B;QAA7BlC,EAAA,CAAAmC,UAAA,SAAAsG,GAAA,CAAA9E,uBAAA,CAA6B;QA2BnC3D,EAAA,CAAAkC,SAAA,IAAuC;QAAvClC,EAAA,CAAAmC,UAAA,eAAAnC,EAAA,CAAA6J,eAAA,IAAAC,GAAA,EAAuC;QAa7C9J,EAAA,CAAAkC,SAAA,GAAe;QAAflC,EAAA,CAAAmC,UAAA,SAAAsG,GAAA,CAAA3E,SAAA,CAAe;QAOf9D,EAAA,CAAAkC,SAAA,EAA0E;QAA1ElC,EAAA,CAAAmC,UAAA,UAAAsG,GAAA,CAAA3E,SAAA,KAAA2E,GAAA,CAAAjF,aAAA,KAAAiF,GAAA,CAAAhF,eAAA,KAAAgF,GAAA,CAAA/E,eAAA,CAA0E;QA4J1D1D,EAAA,CAAAkC,SAAA,EAEnB;QAFmBlC,EAAA,CAAAmC,UAAA,UAAAsG,GAAA,CAAA3E,SAAA,KAAA2E,GAAA,CAAAjF,aAAA,KAAAiF,GAAA,CAAAhF,eAAA,KAAAgF,GAAA,CAAA/E,eAAA,CAEnB;QAIyB1D,EAAA,CAAAkC,SAAA,EAAiC;QAAjClC,EAAA,CAAAmC,UAAA,UAAAsG,GAAA,CAAA3E,SAAA,IAAA2E,GAAA,CAAAjF,aAAA,CAAiC;QAKjCxD,EAAA,CAAAkC,SAAA,EAAmC;QAAnClC,EAAA,CAAAmC,UAAA,UAAAsG,GAAA,CAAA3E,SAAA,IAAA2E,GAAA,CAAA/E,eAAA,CAAmC;QAI5B1D,EAAA,CAAAkC,SAAA,EAAmC;QAAnClC,EAAA,CAAAmC,UAAA,UAAAsG,GAAA,CAAA3E,SAAA,IAAA2E,GAAA,CAAAhF,eAAA,CAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}