import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

@Component({
  selector: 'app-unit-filter',
  templateUrl: './unit-filter.component.html',
  styleUrl: './unit-filter.component.scss'
})
export class UnitFilterComponent {

  unitTypes: { key: string; value: string }[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    finishingType: '',
    status: '',
    unitType: '',
    compoundType: '',
    propertyType: ''
  };

  constructor(private propertyService: PropertyService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.loadUnitTypes();
  }

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

  status: { key: string; value: string }[] = [
    { key: 'NEW', value: 'new' },
    { key: 'AVAILABLE', value: 'available' },
    { key: 'RESERVED', value: 'reserved' },
    { key: 'SOLD', value: 'sold' },
  ];

  compoundTypes: { key: string; value: string }[] = [
    { key: 'Outside Compound', value: 'outside_compound' },
    { key: 'Inside Compound', value: 'inside_compound' },
    { key: 'Village', value: 'village' },
  ];

  propertyTypes: { key: string; value: string }[] = [
    { key: 'Apartments', value: 'apartments' },
    { key: 'Duplexes', value: 'duplexes' },
    { key: 'Studios', value: 'studios' },
    { key: 'Penthouses', value: 'penthouses' },
    { key: 'Basement', value: 'basement' },
    { key: 'Roofs', value: 'roofs' },
    { key: 'Standalone Villas', value: 'standalone_villas' },
    { key: 'Residential Buildings', value: 'residential_buildings' },
    { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },
    { key: 'Warehouses', value: 'warehouses' },
    { key: 'Factories', value: 'factories' },
    { key: 'Administrative Units', value: 'administrative_units' },
    { key: 'Medical Clinics', value: 'medical_clinics' },
    { key: 'Pharmacies', value: 'pharmacies' },
    { key: 'Commercial Stores', value: 'commercial_stores' },
    { key: 'Residential Villa Lands', value: 'residential_villa_lands' },
    { key: 'Residential Buildings Lands', value: 'residential_buildings_lands' },
    { key: 'Administrative Lands', value: 'administrative_lands' },
    { key: 'Commercial Lands', value: 'commercial_lands' },
    { key: 'Medical Lands', value: 'medical_lands' },
    { key: 'Mixed Lands', value: 'mixed_lands' },
    { key: 'Warehouses Land', value: 'warehouses_land' },
    { key: 'Factory Lands', value: 'factory_lands' },
  ];

  apply() {
    this.filtersApplied.emit(this.filter);
  }

  reset() {
    this.filter = {
      finishingType: '',
      status: '',
      unitType: '',
      compoundType: '',
      propertyType: ''
    };
    this.filtersApplied.emit(this.filter);
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Raw API Response:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }
}
