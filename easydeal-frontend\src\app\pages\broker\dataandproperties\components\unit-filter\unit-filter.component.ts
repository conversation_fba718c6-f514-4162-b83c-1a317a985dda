import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { PropertyService } from '../../../services/property.service';

@Component({
  selector: 'app-unit-filter',
  templateUrl: './unit-filter.component.html',
  styleUrl: './unit-filter.component.scss'
})
export class UnitFilterComponent {

  unitTypes: { key: string; value: string }[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    finishingType: '',
    status: '',
    unitType:'',
  };

  constructor( private propertyService: PropertyService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.loadUnitTypes();
  }

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

 status: { key: string; value: string }[] = [
    { key: 'NEW', value: 'new' },
    { key: 'AVAILABLE', value: 'available' },
    { key: 'RESERVED', value: 'reserved' },
    { key: 'SOLD', value: 'sold' },
  ];

  apply() {
    this.filtersApplied.emit(this.filter);
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Raw API Response:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }
}
